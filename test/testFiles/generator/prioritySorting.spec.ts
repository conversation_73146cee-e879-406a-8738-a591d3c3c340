import { Test, TestingModule } from '@nestjs/testing';
import { IEvent, Prettify } from 'ads-layouts-tools';
import {
  IMatchedPlaceholdersToEvents,
  IMatchedPlaceholdersWithAdConfigs,
  PlaceholderType
} from 'InterfacesAndTypes';
import { AdConfigService } from 'src/adConfigs/adConfig.service';
import { DisplayConfigService } from 'src/displayConfig/displayConfig.service';
import { EventsService } from 'src/events/events.service';
import { GeneratorService } from 'src/generator/generator.service';
import { GeneratorHelperClass } from 'src/generator/generator.helper';
import { RuleService } from 'src/rules/rules.service';
import { ServiceToPackageMapService } from 'src/serviceToPackageMap/serviceToPackageMap.service';

describe('GeneratorService - Priority Sorting', () => {
  let service: GeneratorService;
  let helper: GeneratorHelperClass;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GeneratorService,
        GeneratorHelperClass,
        { provide: RuleService, useValue: jest.fn() },
        { provide: AdConfigService, useValue: jest.fn() },
        { provide: DisplayConfigService, useValue: jest.fn() },
        { provide: ServiceToPackageMapService, useValue: jest.fn() },
        { provide: EventsService, useValue: jest.fn() }
      ]
    }).compile();

    service = module.get(GeneratorService);
    helper = module.get(GeneratorHelperClass);
  });

  const createMockEvent = (adConfigGroup: string = 'test_group'): IEvent =>
    ({ type: 'test', params: { adConfigGroup } }) as unknown as IEvent;

  const createMockPlaceholder = (id: string): PlaceholderType => ({ id, type: 'placeholder' });

  // Factory function to create test data
  const createTestData = (
    items: Array<{
      id: string;
      ruleName: string;
      rulePriority: number;
      adConfigGroup?: string;
    }>
  ): IMatchedPlaceholdersToEvents[] => {
    return items.map(item => ({
      successfulEvent: createMockEvent(item.adConfigGroup),
      siteMapMatchedPlaceholders: [createMockPlaceholder(item.id)],
      ruleName: item.ruleName,
      rulePriority: item.rulePriority
    }));
  };

  const expectPlaceholders = (
    placeholders: Prettify<ReturnType<GeneratorHelperClass['sortSuccessfulPlaceholders']>>,
    expectedValues: Array<{ id: string; ruleName: string }>
  ) => {
    expect(placeholders).toHaveLength(expectedValues.length);

    expectedValues.forEach((expected, index) => {
      expect(placeholders[index].placeholder.id).toBe(expected.id);
      expect(placeholders[index].ruleName).toBe(expected.ruleName);
    });
  };

  describe('sortSuccessfulPlaceholders', () => {
    it('should sort placeholders by ID and then by rulePriority when IDs are the same', () => {
      const foundPlaceholdersAndEvents = createTestData([
        { id: '1', ruleName: 'rule1', rulePriority: 5 },
        { id: '1', ruleName: 'rule2', rulePriority: 10 },
        { id: '2', ruleName: 'rule3', rulePriority: 20 }
      ]);

      const sortedPlaceholders = helper['sortSuccessfulPlaceholders'](
        foundPlaceholdersAndEvents
      );

      expectPlaceholders(sortedPlaceholders, [
        { id: '1', ruleName: 'rule2' },
        { id: '1', ruleName: 'rule1' },
        { id: '2', ruleName: 'rule3' }
      ]);
    });

    it('should preserve original order when priorities are the same', () => {
      const foundPlaceholdersAndEvents = createTestData([
        { id: '1', ruleName: 'rule1', rulePriority: 10 },
        { id: '1', ruleName: 'rule2', rulePriority: 10 }
      ]);

      const sortedPlaceholders = helper['sortSuccessfulPlaceholders'](
        foundPlaceholdersAndEvents
      );

      expectPlaceholders(sortedPlaceholders, [
        { id: '1', ruleName: 'rule1' },
        { id: '1', ruleName: 'rule2' }
      ]);
    });

    it('should demonstrate that different priorities change the output order', () => {
      const testCase1 = createTestData([
        { id: '1', ruleName: 'rule1', rulePriority: 10 },
        { id: '1', ruleName: 'rule2', rulePriority: 10 }
      ]);

      const testCase2 = createTestData([
        { id: '1', ruleName: 'rule1', rulePriority: 5 },
        { id: '1', ruleName: 'rule2', rulePriority: 20 }
      ]);

      const result1 = helper['sortSuccessfulPlaceholders'](testCase1);
      const result2 = helper['sortSuccessfulPlaceholders'](testCase2);

      expectPlaceholders(result1, [
        { id: '1', ruleName: 'rule1' },
        { id: '1', ruleName: 'rule2' }
      ]);

      expectPlaceholders(result2, [
        { id: '1', ruleName: 'rule2' },
        { id: '1', ruleName: 'rule1' }
      ]);
    });

    it('should sort placeholders across multiple groups and IDs correctly', () => {
      const foundPlaceholdersAndEvents = createTestData([
        { id: '1', ruleName: 'rule1_group1', rulePriority: 10, adConfigGroup: 'group1' },
        { id: '1', ruleName: 'rule2_group1', rulePriority: 5, adConfigGroup: 'group1' },
        { id: '2', ruleName: 'rule3_group1', rulePriority: 15, adConfigGroup: 'group1' },
        { id: '1', ruleName: 'rule1_group2', rulePriority: 20, adConfigGroup: 'group2' },
        { id: '1', ruleName: 'rule2_group2', rulePriority: 3, adConfigGroup: 'group2' },
        { id: '1', ruleName: 'rule3_group2', rulePriority: 10, adConfigGroup: 'group2' }
      ]);

      const sortedPlaceholders = helper['sortSuccessfulPlaceholders'](
        foundPlaceholdersAndEvents
      );

      const group1Results = sortedPlaceholders.filter(p => p.adConfigGroup === 'group1');
      const group2Results = sortedPlaceholders.filter(p => p.adConfigGroup === 'group2');

      expectPlaceholders(group1Results, [
        { id: '1', ruleName: 'rule1_group1' },
        { id: '1', ruleName: 'rule2_group1' },
        { id: '2', ruleName: 'rule3_group1' }
      ]);

      expectPlaceholders(group2Results, [
        { id: '1', ruleName: 'rule1_group2' },
        { id: '1', ruleName: 'rule3_group2' },
        { id: '1', ruleName: 'rule2_group2' }
      ]);
    });
  });

  describe('handleRulesPriorities', () => {
    it('should prioritize rule priorities over event priorities', () => {
      const mockPlaceholders = [
        {
          id: '1',
          type: 'placeholder',
          rulePriority: 10,
          eventPriority: 1,
          priorityGroup: 'group1'
        },
        {
          id: '1',
          type: 'placeholder',
          rulePriority: 5,
          eventPriority: 10,
          priorityGroup: 'group1'
        }
      ] as IMatchedPlaceholdersWithAdConfigs[];

      const result = service['handleRulesPriorities'](mockPlaceholders);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('1');

      expect(result[0]).not.toHaveProperty('rulePriority');
      expect(result[0]).not.toHaveProperty('eventPriority');
    });

    it('should use event priority as a tiebreaker when rule priorities are equal', () => {
      const mockPlaceholders = [
        {
          id: '1',
          type: 'placeholder',
          rulePriority: 10,
          eventPriority: 5,
          priorityGroup: 'group1'
        },
        {
          id: '1',
          type: 'placeholder',
          rulePriority: 10,
          eventPriority: 2,
          priorityGroup: 'group1'
        }
      ] as IMatchedPlaceholdersWithAdConfigs[];

      const result = service['handleRulesPriorities'](mockPlaceholders);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('1');

      expect(result[0]).not.toHaveProperty('rulePriority');
      expect(result[0]).not.toHaveProperty('eventPriority');
    });
  });
});

import { createMock } from '@golevelup/ts-jest';
import { CACHE_MODULE_OPTIONS, CacheModule } from '@nestjs/cache-manager';
import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import {
  AllConditions,
  AllConditionsSchema,
  AnyConditions,
  AnyConditionsSchema,
  Conditions,
  ConditionsSchema,
  DisplayConfigFiltersService,
  Event,
  EventSchema,
  PlaceholdersDetails,
  Rule,
  RuleSchema,
  ServiceToPackageMap,
  ServiceToPackageMapSchema
} from 'ads-layouts-tools';
import { CommonRequest, IEngineResult } from 'InterfacesAndTypes';
import {
  body_1,
  body_2,
  body_3_AnyFact,
  conditionsDB,
  eventsDB,
  expectedMergeResult_1,
  expectedMergeResult_2,
  expectedMergeResult_3,
  expectedMergeResult_Empty,
  placeholderArray_1,
  placeholderArray_2,
  placeholderArray_3,
  rulesDB,
  serviceToPackageMapDB
} from 'Mocks';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { connect, Connection, Model } from 'mongoose';
import { AdConfigService } from 'src/adConfigs/adConfig.service';
import { CacheService } from 'src/cacheModule/cache.service';
import { DisplayConfigService } from 'src/displayConfig/displayConfig.service';
import { EventsService } from 'src/events/events.service';
import { ExtensionService } from 'src/extensionConfig/extension.service';
import { GeneratorService } from 'src/generator/generator.service';
import { RuleService } from 'src/rules/rules.service';
import { ServiceToPackageMapService } from 'src/serviceToPackageMap/serviceToPackageMap.service';
import { VariantWeightsConfigService } from 'src/variantWeightsConfig/variantWeightsConfig.service';
import { GeneratorHelperClass } from '../../../src/generator/generator.helper';

jest.mock('Utils/logger');

type setupMerge = { allFactsNames: string[]; engineRunResult: IEngineResult };

describe('mergePlaceholdersWithAdConfigsBaseOnEvents test suite', () => {
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;
  let mongoRuleModel: Model<Rule>;
  let mongoConditionsModel: Model<Conditions>;
  let mongoEventsModel: Model<Event>;
  let mongoServiceToPackageMapModel: Model<ServiceToPackageMap>;
  let service: GeneratorService;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;

    mongoConditionsModel = mongoConnection.model(Conditions.name, ConditionsSchema);
    mongoConditionsModel.discriminator(AnyConditions.name, AnyConditionsSchema);
    mongoConditionsModel.discriminator(AllConditions.name, AllConditionsSchema);
    mongoEventsModel = mongoConnection.model(Event.name, EventSchema);
    mongoRuleModel = mongoConnection.model(Rule.name, RuleSchema);
    mongoServiceToPackageMapModel = mongoConnection.model(
      ServiceToPackageMap.name,
      ServiceToPackageMapSchema
    );

    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: false })],
      providers: [
        GeneratorService,
        GeneratorHelperClass,
        RuleService,
        ServiceToPackageMapService,
        EventsService,
        CacheService,
        { useValue: createMock(), provide: AdConfigService },
        { useValue: createMock(), provide: DisplayConfigService },
        { useValue: createMock(), provide: DisplayConfigFiltersService },
        { useValue: createMock(), provide: ExtensionService },
        { useValue: createMock(), provide: VariantWeightsConfigService },
        { provide: getModelToken(Conditions.name), useValue: mongoConditionsModel },
        { provide: getModelToken(Event.name), useValue: mongoEventsModel },
        { provide: getModelToken(Rule.name), useValue: mongoRuleModel },
        {
          provide: getModelToken(ServiceToPackageMap.name),
          useValue: mongoServiceToPackageMapModel
        },
        { provide: CACHE_MODULE_OPTIONS, useValue: { isActive: false } }
      ]
    }).compile();

    service = app.get(GeneratorService);
  });

  beforeEach(async () => {
    await mongoConditionsModel.insertMany(conditionsDB);
    await mongoEventsModel.insertMany(eventsDB);
    await mongoRuleModel.insertMany(rulesDB);
    await mongoServiceToPackageMapModel.insertMany(serviceToPackageMapDB);
  });

  afterEach(async () => {
    await mongoConditionsModel.deleteMany({});
    await mongoEventsModel.deleteMany({});
    await mongoRuleModel.deleteMany({});
    await mongoServiceToPackageMapModel.deleteMany({});
  });

  afterAll(async () => {
    await mongoConnection.dropDatabase();
    await mongoConnection.close();
    await mongod.stop();
  });

  const setupMergePlaceholdersWithAdConfigsBaseOnEvents = async (
    content: CommonRequest
  ): Promise<setupMerge> => {
    const { allFactsNames } = await service['engineSetup'](content);
    const engineRunResult = await service['getRulesEngineRunResult'](content);
    return { allFactsNames, engineRunResult };
  };

  describe('mergePlaceholdersWithAdConfigsBaseOnEvents', () => {
    it('should be defined', () => {
      expect(service['mergePlaceholdersWithAdConfigsBaseOnEvents']).toBeDefined();
    });

    it('should handle empty placeholder array', async () => {
      const { allFactsNames, engineRunResult } =
        await setupMergePlaceholdersWithAdConfigsBaseOnEvents(body_1);

      const emptyPlaceholderArray: PlaceholdersDetails[] = [];

      const result = await service['mergePlaceholdersWithAdConfigsBaseOnEvents'](
        engineRunResult,
        allFactsNames,
        emptyPlaceholderArray,
        body_1.meta.deviceType
      );

      expect(result).toStrictEqual(expectedMergeResult_Empty);
    });

    it('should return correct response - 1', async () => {
      const { allFactsNames, engineRunResult } =
        await setupMergePlaceholdersWithAdConfigsBaseOnEvents(body_1);

      const result = await service['mergePlaceholdersWithAdConfigsBaseOnEvents'](
        engineRunResult,
        allFactsNames,
        placeholderArray_1,
        body_1.meta.deviceType
      );
      expect(result).toStrictEqual(expectedMergeResult_1);
    });

    it('should return correct response - 2', async () => {
      const { allFactsNames, engineRunResult } =
        await setupMergePlaceholdersWithAdConfigsBaseOnEvents(body_2);

      const result = await service['mergePlaceholdersWithAdConfigsBaseOnEvents'](
        engineRunResult,
        allFactsNames,
        placeholderArray_2,
        body_2.meta.deviceType
      );

      expect(result).toStrictEqual(expectedMergeResult_2);
    });

    it('should return correct response - 3', async () => {
      const { allFactsNames, engineRunResult } =
        await setupMergePlaceholdersWithAdConfigsBaseOnEvents(body_3_AnyFact);

      const result = await service['mergePlaceholdersWithAdConfigsBaseOnEvents'](
        engineRunResult,
        allFactsNames,
        placeholderArray_3,
        body_3_AnyFact.meta.deviceType
      );
      expect(result).toStrictEqual(expectedMergeResult_3);
    });
  });
});

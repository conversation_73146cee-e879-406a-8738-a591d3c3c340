import { queryFieldIncludesOrEmptyForSiteVersion as func } from 'Helpers';

// Helper to get $or array with type safety
function getOrConditions(result: ReturnType<typeof func>) {
  expect(result.$or).toBeDefined();
  return result.$or!;
}

describe('queryFieldIncludesOrEmpty', () => {
  describe('Empty request scenarios', () => {
    const requestSiteVersion = '';

    it('should match rules with empty array []', () => {
      const result = func('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $eq: [] } },
          { siteVersion: { $not: { $elemMatch: { $eq: '!empty' } } } },
          { siteVersion: { $exists: false } }
        ]
      });
    });

    it('should be able to match rule ["zoltan", "!k2"] (Sacred Requirement - Rule A)', () => {
      // This rule should match empty requests because it doesn't have !empty
      const result = func('siteVersion', requestSiteVersion);

      // The query structure allows matching rules without !empty
      expect(result.$or).toContainEqual({
        siteVersion: { $not: { $elemMatch: { $eq: '!empty' } } }
      });
    });

    it('should generate query that excludes rules with ["k2", "!empty"]', () => {
      const result = func('siteVersion', requestSiteVersion);

      // Rules with !empty should NOT match, verified by the $not condition
      expect(result.$or).toContainEqual({
        siteVersion: { $not: { $elemMatch: { $eq: '!empty' } } }
      });
    });

    it('should match rules with ["all"] keyword', () => {
      const result = func('siteVersion', requestSiteVersion);

      // Rules with "all" (without !empty) should match via $not condition
      expect(result.$or).toContainEqual({
        siteVersion: { $not: { $elemMatch: { $eq: '!empty' } } }
      });
    });

    it('should exclude rules with ["all", "!empty"]', () => {
      const result = func('siteVersion', requestSiteVersion);

      // The $not: $elemMatch: $eq: "!empty" will NOT match arrays containing !empty
      expect(result.$or).toContainEqual({
        siteVersion: { $not: { $elemMatch: { $eq: '!empty' } } }
      });
    });
  });

  describe('Single value request scenarios', () => {
    it('request="abc" with rule=["!abc"] should NOT match', () => {
      const result = func('siteVersion', 'abc');

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: {
                $in: ['abc', 'all'],
                $nin: ['!abc', '!empty']
              }
            }
          },
          { siteVersion: { $exists: false } }
        ]
      });

      // Query excludes !abc, so rule with ["!abc"] won't match
    });

    it('request="efg" with rule=["efg", "!abc"] should match', () => {
      const result = func('siteVersion', 'efg');

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: {
                $in: ['efg', 'all'],
                $nin: ['!efg', '!empty']
              }
            }
          },
          { siteVersion: { $exists: false } }
        ]
      });

      // Rule ["efg", "!abc"] has "efg" in $in and doesn't have "!efg" in array
    });

    it('request="k2" with rule=["k2", "!empty"] should match (Sacred Requirement)', () => {
      const result = func('siteVersion', 'k2');

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: {
                $in: ['k2', 'all'],
                $nin: ['!k2', '!empty']
              }
            }
          },
          { siteVersion: { $exists: false } }
        ]
      });
    });

    it('request="zoltan" with rule=["zoltan", "!k2"] should match (Sacred Requirement - Rule A)', () => {
      const result = func('siteVersion', 'zoltan');

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: {
                $in: ['zoltan', 'all'],
                $nin: ['!zoltan', '!empty']
              }
            }
          },
          { siteVersion: { $exists: false } }
        ]
      });
    });
  });

  describe('Multi-value request scenarios', () => {
    it('request="abc,efg" with rule=["efg", "!abc"] should NOT match', () => {
      const result = func('siteVersion', 'abc,efg');

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: {
                $in: ['abc', 'efg', 'all'],
                $nin: ['!abc', '!efg', '!empty']
              }
            }
          },
          { siteVersion: { $exists: false } }
        ]
      });

      // Rule has "!abc" so it will NOT match via $nin exclusion
    });

    it('request="abc,efg" with rule=["efg", "abc"] should match', () => {
      const result = func('siteVersion', 'abc,efg');

      // Rule contains both "abc" and "efg", no exclusions violated
      expect(result.$or).toBeDefined();
      expect(result.$or![0]).toEqual({
        siteVersion: {
          $elemMatch: {
            $in: ['abc', 'efg', 'all'],
            $nin: ['!abc', '!efg', '!empty']
          }
        }
      });
    });

    it('request="abc,efg" with rule=["abc"] should match (ANY matching per line 86)', () => {
      const result = func('siteVersion', 'abc,efg');

      // Rule contains "abc" which is in request, so matches via ANY logic
      const orConditions = getOrConditions(result);
      expect(orConditions[0]).toEqual({
        siteVersion: {
          $elemMatch: {
            $in: ['abc', 'efg', 'all'],
            $nin: ['!abc', '!efg', '!empty']
          }
        }
      });
    });

    it('request="abc,efg" with rule=["all"] should match', () => {
      const result = func('siteVersion', 'abc,efg');

      // "all" is included in $in array
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toContain('all');
    });

    it('request="abc,efg" with rule=["all", "!abc"] should NOT match', () => {
      const result = func('siteVersion', 'abc,efg');

      // Query excludes "!abc" in $nin
      expect((result.$or![0] as any).siteVersion.$elemMatch.$nin).toContain('!abc');
    });

    it('request="abc,efg" with rule=["all", "!empty"] should match', () => {
      const result = func('siteVersion', 'abc,efg');

      // Rule has "all" in $in, and "!empty" is in $nin but doesn't prevent match
      // because the rule still has "all" which satisfies $elemMatch
      expect(result.$or![0]).toEqual({
        siteVersion: {
          $elemMatch: {
            $in: ['abc', 'efg', 'all'],
            $nin: ['!abc', '!efg', '!empty']
          }
        }
      });
    });

    it('request="ab_atsdk_ga,zoltan,k2" with rule=["zoltan", "!k2"] should NOT match (Sacred Requirement)', () => {
      const result = func('siteVersion', 'ab_atsdk_ga,zoltan,k2');

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: {
                $in: ['ab_atsdk_ga', 'zoltan', 'k2', 'all'],
                $nin: ['!ab_atsdk_ga', '!zoltan', '!k2', '!empty']
              }
            }
          },
          { siteVersion: { $exists: false } }
        ]
      });

      // Rule has "!k2" so won't match due to $nin exclusion
    });
  });

  describe('CSV Table validation - Row 2: ["zoltan", "!k2"]', () => {
    it('empty request → should match', () => {
      const result = func('siteVersion', '');
      // Query allows matching via $not: { $elemMatch: { $eq: '!empty' } }
      expect(result.$or).toContainEqual({
        siteVersion: { $not: { $elemMatch: { $eq: '!empty' } } }
      });
    });

    it('request="zoltan" → should match', () => {
      const result = func('siteVersion', 'zoltan');
      // Query includes "zoltan" in $in and "!zoltan" in $nin
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toContain('zoltan');
      expect((result.$or![0] as any).siteVersion.$elemMatch.$nin).toContain('!zoltan');
    });

    it('request="zoltan,k2" → should NOT match', () => {
      const result = func('siteVersion', 'zoltan,k2');
      // Query includes "!k2" in $nin, which will exclude rule with "!k2"
      expect((result.$or![0] as any).siteVersion.$elemMatch.$nin).toContain('!k2');
    });

    it('request="k2" → should NOT match', () => {
      const result = func('siteVersion', 'k2');
      // Query includes "!k2" in $nin
      expect((result.$or![0] as any).siteVersion.$elemMatch.$nin).toContain('!k2');
    });

    it('request="inna,zoltan" → should match', () => {
      const result = func('siteVersion', 'inna,zoltan');
      // Query includes "zoltan" in $in, and "!k2" is not in $nin (only !inna and !zoltan are)
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toContain('zoltan');
      expect((result.$or![0] as any).siteVersion.$elemMatch.$nin).not.toContain('!k2');
    });
  });

  describe('CSV Table validation - Row 3: ["abc", "k2", "!empty"]', () => {
    it('empty request → should NOT match', () => {
      const result = func('siteVersion', '');
      // Query structure will NOT match rules with !empty via $not condition
      expect(result.$or).toContainEqual({
        siteVersion: { $not: { $elemMatch: { $eq: '!empty' } } }
      });
    });

    it('request="zoltan" → should NOT match (zoltan not in whitelist)', () => {
      const result = func('siteVersion', 'zoltan');
      // Rule has ["abc", "k2", "!empty"], request has "zoltan"
      // Query will look for "zoltan" or "all" in rule, but rule doesn't have them
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toEqual(['zoltan', 'all']);
    });

    it('request="zoltan,k2" → should match (k2 in whitelist)', () => {
      const result = func('siteVersion', 'zoltan,k2');
      // Query includes "k2" in $in
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toContain('k2');
    });

    it('request="k2" → should match', () => {
      const result = func('siteVersion', 'k2');
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toContain('k2');
    });
  });

  describe('CSV Table validation - Row 4: ["k2", "!empty"]', () => {
    it('empty request → should NOT match', () => {
      const result = func('siteVersion', '');
      expect(result.$or).toContainEqual({
        siteVersion: { $not: { $elemMatch: { $eq: '!empty' } } }
      });
    });

    it('request="zoltan" → should NOT match', () => {
      const result = func('siteVersion', 'zoltan');
      // Rule only has "k2", request has "zoltan" - no match
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toEqual(['zoltan', 'all']);
    });

    it('request="zoltan,k2" → should match (has k2)', () => {
      const result = func('siteVersion', 'zoltan,k2');
      // Query includes "k2" in $in which will match
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toContain('k2');
    });

    it('request="k2" → should match', () => {
      const result = func('siteVersion', 'k2');
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toContain('k2');
    });
  });

  describe('CSV Table validation - Row 7: ["all"]', () => {
    it('empty request → should match', () => {
      const result = func('siteVersion', '');
      // Matches via $not condition (no !empty in array)
      expect(result.$or).toContainEqual({
        siteVersion: { $not: { $elemMatch: { $eq: '!empty' } } }
      });
    });

    it('request="zoltan" → should match', () => {
      const result = func('siteVersion', 'zoltan');
      // "all" is in $in array
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toContain('all');
    });

    it('request="zoltan,k2" → should match', () => {
      const result = func('siteVersion', 'zoltan,k2');
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toContain('all');
    });

    it('request="k2" → should match', () => {
      const result = func('siteVersion', 'k2');
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toContain('all');
    });

    it('request="inna,zoltan" → should match', () => {
      const result = func('siteVersion', 'inna,zoltan');
      expect((result.$or![0] as any).siteVersion.$elemMatch.$in).toContain('all');
    });
  });

  describe('Edge cases and special scenarios', () => {
    it('should work with different field names', () => {
      const result = func('pageId', 'test123');

      expect(result.$or![0]).toHaveProperty('pageId');
      expect((result.$or![0] as any).pageId.$elemMatch.$in).toContain('test123');
    });

    it('should handle realistic siteVersion values', () => {
      const result = func('siteVersion', 'ab_atsdk_ga');

      expect((result.$or![0] as any).siteVersion.$elemMatch).toEqual({
        $in: ['ab_atsdk_ga'],
        $nin: ['!ab_atsdk_ga', '!empty']
      });
    });

    it('should handle multiple realistic values', () => {
      const result = func('siteVersion', 'ab_atsdk_ga,ab_atsdk_gb');

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: {
                $in: ['ab_atsdk_ga', 'ab_atsdk_gb'],
                $nin: ['!ab_atsdk_ga', '!ab_atsdk_gb', '!empty']
              }
            }
          },
          { siteVersion: { $eq: [] } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $elemMatch: { $eq: 'all' } } }
        ]
      });
    });
  });
});

import { queryFieldIncludesOrEmpty } from 'Helpers';

describe('queryFieldIncludesOrEmpty', () => {
  describe('when request has no siteVersion (empty string)', () => {
    const requestSiteVersion = '';

    it('should match rule with siteVersion: ["zoltan", "!k2"]', () => {
      // Rule: when siteVersion does not contain "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      // Expected: should match rules with empty arrays, undefined, or arrays containing empty string
      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: [''], $nin: [] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["abc", "k2", "!empty"]', () => {
      // Rule: when siteVersion contains "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      // Expected: should match rules with empty arrays, undefined, or arrays containing empty string
      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: [''], $nin: [] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["k2", "!empty"]', () => {
      // Rule: when siteVersion equals "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: [''], $nin: [] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["zoltan", "!k2"]', () => {
      // Rule: when siteVersion is not equal to "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: [''], $nin: [] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["all"]', () => {
      // Rule: no condition based on siteVersion
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: [''], $nin: [] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });
  });

  describe('when request has siteVersion: "zoltan"', () => {
    const requestSiteVersion = 'zoltan';

    it('should match rule with siteVersion: ["zoltan", "!k2"]', () => {
      // Rule: when siteVersion does not contain "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['zoltan'], $nin: ['!zoltan', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should NOT match rule with siteVersion: ["abc", "k2", "!empty"]', () => {
      // Rule: when siteVersion contains "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['zoltan'], $nin: ['!zoltan', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should NOT match rule with siteVersion: ["k2", "!empty"]', () => {
      // Rule: when siteVersion equals "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['zoltan'], $nin: ['!zoltan', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["zoltan", "!k2"]', () => {
      // Rule: when siteVersion is not equal to "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['zoltan'], $nin: ['!zoltan', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["zoltan", "!empty"]', () => {
      // Rule: when siteVersion exists and has any value
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['zoltan'], $nin: ['!zoltan', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["all"]', () => {
      // Rule: no condition based on siteVersion
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['zoltan'], $nin: ['!zoltan', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });
  });

  describe('when request has siteVersion: "zoltan,k2" (comma-separated)', () => {
    const requestSiteVersion = 'zoltan,k2';

    it('should NOT match rule with siteVersion: ["zoltan", "!k2"]', () => {
      // Rule: when siteVersion does not contain "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['zoltan', 'k2'], $nin: ['!zoltan', '!k2', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["abc", "k2", "!empty"]', () => {
      // Rule: when siteVersion contains "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['zoltan', 'k2'], $nin: ['!zoltan', '!k2', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should NOT match rule with siteVersion: ["k2", "!empty"]', () => {
      // Rule: when siteVersion equals "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['zoltan', 'k2'], $nin: ['!zoltan', '!k2', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["zoltan", "!k2"]', () => {
      // Rule: when siteVersion is not equal to "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['zoltan', 'k2'], $nin: ['!zoltan', '!k2', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["zoltan", "!empty"]', () => {
      // Rule: when siteVersion exists and has any value
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['zoltan', 'k2'], $nin: ['!zoltan', '!k2', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["all"]', () => {
      // Rule: no condition based on siteVersion
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['zoltan', 'k2'], $nin: ['!zoltan', '!k2', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });
  });

  describe('when request has siteVersion: "k2"', () => {
    const requestSiteVersion = 'k2';

    it('should NOT match rule with siteVersion: ["zoltan", "!k2"]', () => {
      // Rule: when siteVersion does not contain "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['k2'], $nin: ['!k2', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["abc", "k2", "!empty"]', () => {
      // Rule: when siteVersion contains "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['k2'], $nin: ['!k2', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["k2", "!empty"]', () => {
      // Rule: when siteVersion equals "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['k2'], $nin: ['!k2', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should NOT match rule with siteVersion: ["zoltan", "!k2"]', () => {
      // Rule: when siteVersion is not equal to "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['k2'], $nin: ['!k2', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["k2", "!empty"]', () => {
      // Rule: when siteVersion exists and has any value
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['k2'], $nin: ['!k2', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["all"]', () => {
      // Rule: no condition based on siteVersion
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['k2'], $nin: ['!k2', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });
  });

  describe('when request has siteVersion: "inna,zoltan" (different order)', () => {
    const requestSiteVersion = 'inna,zoltan';

    it('should match rule with siteVersion: ["zoltan", "!k2"]', () => {
      // Rule: when siteVersion does not contain "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['inna', 'zoltan'], $nin: ['!inna', '!zoltan', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should NOT match rule with siteVersion: ["abc", "k2", "!empty"]', () => {
      // Rule: when siteVersion contains "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['inna', 'zoltan'], $nin: ['!inna', '!zoltan', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should NOT match rule with siteVersion: ["k2", "!empty"]', () => {
      // Rule: when siteVersion equals "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['inna', 'zoltan'], $nin: ['!inna', '!zoltan', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["zoltan", "!k2"]', () => {
      // Rule: when siteVersion is not equal to "k2"
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['inna', 'zoltan'], $nin: ['!inna', '!zoltan', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["zoltan", "!empty"]', () => {
      // Rule: when siteVersion exists and has any value
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['inna', 'zoltan'], $nin: ['!inna', '!zoltan', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should match rule with siteVersion: ["all"]', () => {
      // Rule: no condition based on siteVersion
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['inna', 'zoltan'], $nin: ['!inna', '!zoltan', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });
  });

  describe('realistic siteVersion values from codebase', () => {
    it('should handle siteVersion: "ab_atsdk_ga"', () => {
      const requestSiteVersion = 'ab_atsdk_ga';
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['ab_atsdk_ga'], $nin: ['!ab_atsdk_ga', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should handle siteVersion: "ab_atsdk_gb"', () => {
      const requestSiteVersion = 'ab_atsdk_gb';
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['ab_atsdk_gb'], $nin: ['!ab_atsdk_gb', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should handle multiple realistic values: "ab_atsdk_ga,ab_atsdk_gb"', () => {
      const requestSiteVersion = 'ab_atsdk_ga,ab_atsdk_gb';
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: {
                $in: ['ab_atsdk_ga', 'ab_atsdk_gb'],
                $nin: ['!ab_atsdk_ga', '!ab_atsdk_gb', '!empty']
              }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });
  });

  describe('edge cases', () => {
    it('should handle single character values', () => {
      const requestSiteVersion = 'a';
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          { siteVersion: { $elemMatch: { $in: ['a'], $nin: ['!a', '!empty'] } } },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should handle values with special characters', () => {
      const requestSiteVersion = 'site-version_1.0';
      const result = queryFieldIncludesOrEmpty('siteVersion', requestSiteVersion);

      expect(result).toEqual({
        $or: [
          {
            siteVersion: {
              $elemMatch: { $in: ['site-version_1.0'], $nin: ['!site-version_1.0', '!empty'] }
            }
          },
          { siteVersion: { $exists: false } },
          { siteVersion: { $eq: [] } }
        ]
      });
    });

    it('should work with different field names', () => {
      const requestValue = 'test';
      const result = queryFieldIncludesOrEmpty('pageId', requestValue);

      expect(result).toEqual({
        $or: [
          { pageId: { $elemMatch: { $in: ['test'], $nin: ['!test', '!empty'] } } },
          { pageId: { $exists: false } },
          { pageId: { $eq: [] } }
        ]
      });
    });
  });
});

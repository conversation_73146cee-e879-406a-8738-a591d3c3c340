import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import {
  DisplayConfig,
  DisplayConfigSchema,
  DisplayConfigFiltersModule
} from 'ads-layouts-tools';
import * as dayjs from 'dayjs';
import { commonMockDisplayConfigData } from 'Mocks';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { connect, Connection, Model } from 'mongoose';
import { CacheModule } from 'src/cacheModule/cache.module';
import { DisplayConfigService } from 'src/displayConfig/displayConfig.service';

jest.mock('Utils/logger');

describe('DisplayConfig service test suite', () => {
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;
  let mongoDisplayConfigModel: Model<DisplayConfig>;
  let service: DisplayConfigService;
  let mongoTable: DisplayConfig[];

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;

    mongoDisplayConfigModel = mongoConnection.model(DisplayConfig.name, DisplayConfigSchema);

    const app: TestingModule = await Test.createTestingModule({
      imports: [
        CacheModule.register({ isActive: false }),
        DisplayConfigFiltersModule.configure(() => {})
      ],
      providers: [
        DisplayConfigService,
        {
          provide: getModelToken(DisplayConfig.name),
          useValue: mongoDisplayConfigModel
        }
      ]
    }).compile();

    service = app.get(DisplayConfigService);
  });

  beforeEach(async () => {
    mongoTable = [
      {
        ...commonMockDisplayConfigData,
        service: 'ddtvn',
        siteVersion: `AFTER:${dayjs('2024-05-29T13:00:00').format('YYYY-MM-DDTHH:mm:ss')}`,
        release: 'release/1.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'ddtvn',
        siteVersion: `AFTER:${dayjs('2023-04-29T15:00:00').format('YYYY-MM-DDTHH:mm:ss')}`,
        release: 'release/2.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'ddtvn',
        siteVersion: 'RELEASE',
        release: 'release/3.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'ddtvn',
        siteVersion: 'ab_atsdk_ga',
        release: 'release/4.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'ddtvn',
        siteVersion: 'ab_adph_ga,ab_cwv_gb',
        release: 'release/5.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'tvn',
        release: 'release/6.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'tvn',
        release: 'release/7.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'tvn24',
        release: 'release/8.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'default',
        release: 'release/9.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'A',
        release: 'release/9.0.1'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'B',
        release: 'release/9.0.2'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'A,B',
        release: 'release/9.0.3'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'B,C',
        release: 'release/9.0.4'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'A,B,C',
        release: 'release/9.0.5'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'D,E,F',
        release: 'release/9.0.6'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'D,E,G',
        release: 'release/9.0.7'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'H',
        release: 'release/9.0.8'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'I',
        release: 'release/9.0.9'
      }
    ];
    await mongoDisplayConfigModel.insertMany(mongoTable);
  });

  afterEach(async () => {
    await mongoDisplayConfigModel.deleteMany({});
  });

  afterAll(async () => {
    await mongoConnection.dropDatabase();
    await mongoConnection.close();
    await mongod.stop();
  });

  describe('DisplayConfigService', () => {
    // TODO: add test for selectDisplayConfigs
    describe('selectDisplayConfigs', () => {
      it('should return correct value', async () => {
        const serviceId = 'tvn24';
        const time = 'TIME';

        const [result, ...rest] = await service.selectDisplayConfigs(time, serviceId);

        expect(rest.length).toEqual(0);
        expect(result).toMatchObject({
          service: serviceId,
          release: 'release/8.0.0'
        });
      });

      it('should return correct list of configs', async () => {
        const serviceId = 'ddtvn';
        const time = 'TIME';

        const result = await service.selectDisplayConfigs(time, serviceId);

        expect(result.length).toEqual(5);
      });
    });
  });
});

import {
  createHeaderSchema,
  kebabCaseToCamelCase,
  optionalHeaders,
  requiredHeaders,
  StringValidationType
} from 'Helpers';
import {
  BodyToHeaderParse,
  ContentMeta,
  ContentMetaOptionalFields,
  ContentMetaRequiredFields
} from 'InterfacesAndTypes';
import { AdConfigDeviceTypeEnum } from 'ads-layouts-tools';
import * as joi from 'joi';

const zip2Arrays = <T, P>(array1: T[], array2: P[]): [T, P][] =>
  array1.map((_, c) => [array1[c], array2[c]]);

const compareHeadersToBody = <T extends Partial<ContentMeta>>(
  headers: Array<keyof BodyToHeaderParse<T>>,
  bodyMeta: Partial<T>,
  result: StringValidationType<BodyToHeaderParse<T>>
): void => {
  const bodyMetaValues = Object.values(bodyMeta);
  zip2Arrays(headers, bodyMetaValues).forEach(([header, metaValue]) =>
    expect(result[header].validate(encodeURIComponent(metaValue)).error).toBeUndefined()
  );
};

describe('joiValidation - test suite', () => {
  describe('kebabCaseToCamelCase', () => {
    it('should convert kebab-case string to camel-case', () => {
      const result = kebabCaseToCamelCase('hello-world');

      expect(result).toBe('helloWorld');
    });

    it("should remove 'x-at-' prefix from input string", () => {
      const input = 'x-at-example-string';
      const expectedOutput = 'exampleString';
      const result = kebabCaseToCamelCase(input);
      expect(result).toBe(expectedOutput);
    });

    it('should return an empty string when input is only "x-at-"', () => {
      const input = 'x-at-';
      const result = kebabCaseToCamelCase(input);
      expect(result).toBe('');
    });

    it('should convert very long kebab-case string to camelCase', () => {
      const longKebabCaseString =
        'this-is-a-very-long-kebab-case-string-with-many-parts-to-test-the-function-correctness';
      const expectedCamelCaseString =
        'thisIsAVeryLongKebabCaseStringWithManyPartsToTestTheFunctionCorrectness';

      const result = kebabCaseToCamelCase(longKebabCaseString);

      expect(result).toBe(expectedCamelCaseString);
    });

    it('should not remove "x-at-" when it is in the middle', () => {
      const input = 'some-x-at-value';
      const expectedOutput = 'someXAtValue';
      const result = kebabCaseToCamelCase(input);
      expect(result).toBe(expectedOutput);
    });
  });

  describe('createHeaderSchema', () => {
    it('should create a valid Joi schema with required fields', () => {
      const deviceType = AdConfigDeviceTypeEnum.DESKTOP;
      const locationInfoPageType = 'page';
      const locationInfoSectionId = '456';
      const locationInfoSectionName = 'section';
      const serviceId = 'ABC';
      const siteVersionIdentifier = 'hash123';
      const time = '1234567890';

      const bodyMeta: ContentMetaRequiredFields = {
        deviceType,
        locationInfoPageType,
        locationInfoSectionId,
        locationInfoSectionName,
        serviceId,
        siteVersionIdentifier,
        time
      };
      const isOptional = false;

      const result = createHeaderSchema(requiredHeaders, bodyMeta, isOptional);

      expect(result).toStrictEqual({
        'x-at-device-type': joi.string().valid(encodeURIComponent('desktop')).required(),
        'x-at-location-info-page-type': joi
          .string()
          .valid(encodeURIComponent(locationInfoPageType))
          .required(),
        'x-at-location-info-section-id': joi
          .string()
          .valid(encodeURIComponent(locationInfoSectionId))
          .required(),
        'x-at-location-info-section-name': joi
          .string()
          .valid(encodeURIComponent(locationInfoSectionName))
          .required(),
        'x-at-service-id': joi.string().valid(encodeURIComponent(serviceId)).required(),
        'x-at-site-version-identifier': joi
          .string()
          .valid(encodeURIComponent(siteVersionIdentifier))
          .required(),
        'x-at-time': joi.string().valid(encodeURIComponent(time)).required()
      });
    });

    it('should return empty schema when headers array is empty', () => {
      const headers: Array<keyof BodyToHeaderParse<ContentMetaOptionalFields>> = [];
      const bodyMeta: ContentMetaOptionalFields = {};
      const isOptional = true;

      const result = createHeaderSchema(headers, bodyMeta, isOptional);

      expect(result).toEqual({});
      expect(Object.keys(result).length).toBe(0);
    });

    it('should encode header values using encodeURIComponent when headers and bodyMeta are provided', () => {
      const headers: Array<Partial<keyof BodyToHeaderParse<ContentMetaRequiredFields>>> = [
        'x-at-device-type',
        'x-at-location-info-page-type'
      ];

      const bodyMeta: Partial<ContentMetaRequiredFields> = {
        deviceType: AdConfigDeviceTypeEnum.TABLET,
        locationInfoPageType: '123_abc/<>|&@'
      };
      const isOptional = false;

      const result = createHeaderSchema(headers, bodyMeta, isOptional);

      compareHeadersToBody(headers, bodyMeta, result);
    });

    it('should return an schema with all optional headers forbidden when headers are optional and not present in bodyMeta', () => {
      const bodyMeta: ContentMetaOptionalFields = {};
      const isOptional = true;

      const result = createHeaderSchema(optionalHeaders, bodyMeta, isOptional);

      const isEverySingleResultForbidden = Object.values(result).every(value => {
        const valueDescription = value.describe() as joi.Description & {
          flags: { presence?: string };
        };

        return valueDescription?.flags?.presence === 'forbidden';
      });

      expect(Object.keys(result).length).toEqual(optionalHeaders.length);
      expect(isEverySingleResultForbidden).toEqual(true);
    });

    it('should handle required headers with empty string values correctly', () => {
      const headers: Array<Partial<keyof BodyToHeaderParse<ContentMetaRequiredFields>>> = [
        'x-at-service-id',
        'x-at-location-info-page-type'
      ];

      const bodyMeta: Partial<ContentMeta> = {
        serviceId: '',
        locationInfoPageType: ''
      };
      const isOptional = false;

      const result = createHeaderSchema(headers, bodyMeta, isOptional);

      compareHeadersToBody(headers, bodyMeta, result);
    });

    it('should handle optional headers with empty string values correctly', () => {
      const headers: Array<Partial<keyof BodyToHeaderParse<ContentMetaOptionalFields>>> = [
        'x-at-paywall',
        'x-at-rules-package'
      ];

      const bodyMeta: Partial<ContentMeta> = {
        paywall: '',
        rulesPackage: ''
      };
      const isOptional = true;

      const result = createHeaderSchema(headers, bodyMeta, isOptional);

      compareHeadersToBody(headers, bodyMeta, result);
    });
  });
});

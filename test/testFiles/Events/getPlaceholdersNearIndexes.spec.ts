import { createMock } from '@golevelup/ts-jest';
import { Test, TestingModule } from '@nestjs/testing';
import { Event, PlaceholderPositionEnum } from 'ads-layouts-tools';
import { FactType } from 'InterfacesAndTypes';
import { facts } from 'Mocks';
import { CacheModule } from 'src/cacheModule/cache.module';
import { EventsService } from 'src/events/events.service';
import { GetPlaceholdersNearIndexesClass } from 'src/events/getPlaceholderHelpers/GetPlaceholdersNearIndexes.class';
import { VariantWeightsConfigService } from 'src/variantWeightsConfig/variantWeightsConfig.service';

jest.mock('Utils/logger');

describe('getNearToElementPlaceholder test suite', () => {
  let placeholdersNearIndexesHelper: GetPlaceholdersNearIndexesClass;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: false })],
      providers: [
        EventsService,
        {
          provide: VariantWeightsConfigService,
          useValue: createMock()
        }
      ]
    }).compile();

    placeholdersNearIndexesHelper = new GetPlaceholdersNearIndexesClass();
  });

  describe('getPlaceholdersNearIndexes', () => {
    it('should return placeholders under matching elements when position is UNDER', () => {
      const event = {
        params: {
          placeholder: {
            element: ['paragraph'],
            elementsIndexes: [1, 3, 5, 9],
            position: PlaceholderPositionEnum.UNDER
          }
        }
      } as Event;

      const result = placeholdersNearIndexesHelper.get(event, facts);

      expect(result).toStrictEqual([
        { id: '3', type: 'placeholder' },
        { id: '7', type: 'placeholder' },
        { id: '13', type: 'placeholder' },
        { id: '21', type: 'placeholder' }
      ]);
    });

    it('should return empty array when facts array is empty', () => {
      const event = {
        params: {
          placeholder: {
            element: ['paragraph'],
            elementsIndexes: [1],
            position: PlaceholderPositionEnum.UNDER
          }
        }
      } as Event;

      const facts: FactType[] = [];

      const result = placeholdersNearIndexesHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should reverse facts array when countBackwards is true', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            element: ['paragraph'],
            elementsIndexes: [1, 5, 9, 13],
            countBackwards: true
          }
        }
      } as Event;

      const result = placeholdersNearIndexesHelper.get(event, facts);

      expect(result).toStrictEqual([
        { id: '33', type: 'placeholder' },
        { id: '19', type: 'placeholder' },
        { id: '11', type: 'placeholder' },
        { id: '1', type: 'placeholder' }
      ]);
    });

    it('should return empty array when element parameter is undefined', async () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            elementsIndexes: [1, 2, 3],
            countBackwards: false,
            element: undefined
          }
        }
      } as Event;

      const result = placeholdersNearIndexesHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should return empty array when no matching elements found in facts', async () => {
      const event = {
        params: {
          placeholder: {
            element: ['nonexistentType'],
            elementsIndexes: [1, 2, 3],
            position: PlaceholderPositionEnum.UNDER
          }
        }
      } as Event;

      const result = placeholdersNearIndexesHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should return an empty array when elementsIndexes is empty', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            element: ['placeholder'],
            elementsIndexes: []
          }
        }
      } as unknown as Event;

      const result = placeholdersNearIndexesHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should return empty array when updatedIndex is out of bounds - UNDER', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            element: ['text'],
            elementsIndexes: [1]
          }
        }
      } as Event;

      const facts = [{ id: '1', type: 'text' }] as FactType[];

      const result = placeholdersNearIndexesHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should return empty array when updatedIndex is out of bounds - ABOVE', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.ABOVE,
            element: ['text'],
            elementsIndexes: [1]
          }
        }
      } as Event;

      const facts = [{ id: '1', type: 'text' }] as FactType[];

      const result = placeholdersNearIndexesHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should return empty array when matching elements exist but no valid placeholders are found - UNDER', async () => {
      const event = {
        params: {
          placeholder: {
            element: ['paragraph'],
            elementsIndexes: [1],
            position: PlaceholderPositionEnum.UNDER
          }
        }
      } as Event;

      const facts = [
        { id: '1', type: 'paragraph' },
        { id: '2', type: 'paragraph' }
      ] as FactType[];

      const result = placeholdersNearIndexesHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should return empty array when matching elements exist but no valid placeholders are found - ABOVE', async () => {
      const event = {
        params: {
          placeholder: {
            element: ['paragraph'],
            elementsIndexes: [2],
            position: PlaceholderPositionEnum.ABOVE
          }
        }
      } as Event;

      const facts = [
        { id: '1', type: 'paragraph' },
        { id: '2', type: 'paragraph' }
      ] as FactType[];

      const result = placeholdersNearIndexesHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should not modify the original fact array when invoked multiple times', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            element: ['paragraph'],
            elementsIndexes: [1, 2],
            countBackwards: true
          }
        }
      } as Event;

      const facts = [
        { id: '1', type: 'placeholder' },
        { id: '2', type: 'paragraph' },
        { id: '3', type: 'placeholder' }
      ] as FactType[];

      const result_backward = placeholdersNearIndexesHelper.get(event, facts);

      event.params.placeholder.countBackwards = false;

      const result_forward = placeholdersNearIndexesHelper.get(event, facts);

      expect(result_forward).toEqual([{ id: '3', type: 'placeholder' }]);
      expect(result_backward).toEqual([{ id: '1', type: 'placeholder' }]);
    });

    it('should catch first available type of element', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            element: ['nonexistent', 'html', 'paragraph'],
            elementsIndexes: [1, 2]
          }
        }
      } as Event;

      const result = placeholdersNearIndexesHelper.get(event, facts);

      expect(result).toEqual([
        { id: '23', type: 'placeholder' },
        { id: '29', type: 'placeholder' }
      ]);
    });
  });
});

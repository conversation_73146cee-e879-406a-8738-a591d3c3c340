import { createMock } from '@golevelup/ts-jest';
import { Test, TestingModule } from '@nestjs/testing';
import { Event, PlaceholderPositionEnum } from 'ads-layouts-tools';
import { FactType } from 'InterfacesAndTypes';
import { facts } from 'Mocks';
import { CacheModule } from 'src/cacheModule/cache.module';
import { EventsService } from 'src/events/events.service';
import { GetNearToElementPlaceholderClass } from 'src/events/getPlaceholderHelpers/GetNearToElementPlaceholder.class';
import { VariantWeightsConfigService } from 'src/variantWeightsConfig/variantWeightsConfig.service';

jest.mock('Utils/logger');

describe('getNearToElementPlaceholder test suite', () => {
  let nearToElementPlaceholderHelper: GetNearToElementPlaceholderClass;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: false })],
      providers: [
        EventsService,
        {
          provide: VariantWeightsConfigService,
          useValue: createMock()
        }
      ]
    }).compile();

    nearToElementPlaceholderHelper = new GetNearToElementPlaceholderClass();
  });

  describe('getNearToElementPlaceholder', () => {
    it('should return placeholder under matching element when position is UNDER', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            element: ['photo']
          }
        }
      } as Event;

      const result = nearToElementPlaceholderHelper.get(event, facts);

      expect(result).toStrictEqual([{ id: '11', type: 'placeholder' }]);
    });

    it('should return empty array when no matching element found', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            element: ['nonexistent']
          }
        }
      } as Event;

      const result = nearToElementPlaceholderHelper.get(event, facts);

      expect(result).toStrictEqual([]);
    });

    it('should return empty array when the matching element is out of range - ABOVE', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.ABOVE,
            element: ['text']
          }
        }
      } as Event;

      const facts = [{ id: '1', type: 'text' }] as FactType[];

      const result = nearToElementPlaceholderHelper.get(event, facts);

      expect(result).toStrictEqual([]);
    });

    it('should return empty array when the matching element is out of range - UNDER', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            element: ['text']
          }
        }
      } as Event;

      const facts = [{ id: '1', type: 'text' }] as FactType[];

      const result = nearToElementPlaceholderHelper.get(event, facts);

      expect(result).toStrictEqual([]);
    });

    it('should return the first matching placeholder when multiple exist', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            element: ['photo', 'html']
          }
        }
      } as Event;

      const result = nearToElementPlaceholderHelper.get(event, facts);

      expect(result).toEqual([{ id: '11', type: 'placeholder' }]);
    });

    it('should return an empty array when element array is empty', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            element: undefined
          }
        }
      } as unknown as Event;

      const result = nearToElementPlaceholderHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should return an empty array when picked element is not a placeholder - UNDER', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.UNDER,
            element: ['text']
          }
        }
      } as Event;

      const facts = [
        { id: '1', type: 'text' },
        { id: '2', type: 'text' }
      ] as FactType[];

      const result = nearToElementPlaceholderHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should return an empty array when picked element is not a placeholder - ABOVE', () => {
      const event = {
        params: {
          placeholder: {
            position: PlaceholderPositionEnum.ABOVE,
            element: ['text']
          }
        }
      } as Event;

      const facts = [
        { id: '1', type: 'text' },
        { id: '2', type: 'text' }
      ] as FactType[];

      const result = nearToElementPlaceholderHelper.get(event, facts);

      expect(result).toEqual([]);
    });
  });
});

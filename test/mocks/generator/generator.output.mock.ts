import { AdConfigAdServerEnum, AdConfigDeviceTypeEnum } from 'ads-layouts-tools';
import {
  IAdsConfigIdentificationData,
  IGeneratorResponse,
  IRulesPriorities,
  PageTypeEnum
} from 'InterfacesAndTypes';

const { DESKTOP, SMARTPHONE, TABLET } = AdConfigDeviceTypeEnum;
const { ADOCEAN, GAM } = AdConfigAdServerEnum;

export const outputMock_base: IGeneratorResponse = {
  debugData: {
    releaseVersion: 'release/1.63.0/',
    allAvailableAdConfigGroups: [
      'group: lewy_margines, id: 1',
      'group: panel_pod_artykulem, id: 5',
      'group: panel_pod_artykulem, id: 6',
      'group: panel_pod_artykulem, id: 7',
      'group: panel_pod_artykulem, id: 8',
      'group: panel_pod_artykulem, id: 9',
      'group: adex_detal, id: 1',
      'group: layer, id: 1',
      'group: on_top, id: 1',
      'group: branding_playera_main, id: 1',
      'group: baner_detal, id: 1',
      'group: baner_detal, id: 2',
      'group: baner_detal, id: 3',
      'group: baner_detal, id: 4',
      'group: baner_detal, id: 5',
      'group: baner_detal, id: 6',
      'group: baner_detal, id: 7',
      'group: baner_detal, id: 8',
      'group: baner_detal, id: 9',
      'group: baner_detal, id: 10',
      'group: panel_pod_artykulem, id: 1',
      'group: panel_pod_artykulem, id: 2',
      'group: panel_pod_artykulem, id: 3',
      'group: panel_pod_artykulem, id: 4',
      'group: prawa_szpalta, id: 1'
    ],
    allRulesCount: 14,
    successConditionsCount: 13,
    successEventsCount: 13,
    successMergeCount: 9,
    shortSuccessMergeStats: [
      { placeholderId: '2', group: 'on_top', groupId: '1' },
      { placeholderId: '1', group: 'layer', groupId: '1' },
      { placeholderId: '23', group: 'baner_detal', groupId: '1' },
      { placeholderId: '46', group: 'baner_detal', groupId: '2' },
      { placeholderId: '74', group: 'panel_pod_artykulem', groupId: '1' },
      { placeholderId: '78', group: 'panel_pod_artykulem', groupId: '2' },
      { placeholderId: '82', group: 'panel_pod_artykulem', groupId: '3' },
      { placeholderId: '86', group: 'panel_pod_artykulem', groupId: '4' },
      { placeholderId: '90', group: 'panel_pod_artykulem', groupId: '5' },
      { placeholderId: '98', group: 'panel_pod_artykulem', groupId: '6' },
      { placeholderId: '108', group: 'panel_pod_artykulem', groupId: '7' },
      { placeholderId: '118', group: 'panel_pod_artykulem', groupId: '8' },
      { placeholderId: '128', group: 'panel_pod_artykulem', groupId: '9' },
      { placeholderId: '12', group: 'branding_playera_main', groupId: '1' },
      { placeholderId: '154', group: 'prawa_szpalta', groupId: '1' },
      { placeholderId: '158', group: 'lewy_margines', groupId: '1' },
      { placeholderId: '69', group: 'adex_detal', groupId: '1' }
    ],
    rulesStats: {
      success: [
        {
          ruleName: 'onTopZeScreeningiem',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '2' }],
          mergePass: true,
          mergeResult: [
            {
              id: '2',
              type: 'placeholder',
              AD_Config_group: 'on_top',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'onTop2',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '1' }],
          mergePass: true,
          mergeResult: [
            {
              id: '1',
              type: 'placeholder',
              AD_Config_group: 'layer',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'banerDetal1',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '23' }],
          mergePass: true,
          mergeResult: [
            {
              id: '23',
              type: 'placeholder',
              AD_Config_group: 'baner_detal',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'panelPodXArtykulem',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { type: 'placeholder', id: '74' },
            { type: 'placeholder', id: '78' },
            { type: 'placeholder', id: '82' },
            { type: 'placeholder', id: '86' },
            { type: 'placeholder', id: '90' },
            { type: 'placeholder', id: '98' },
            { type: 'placeholder', id: '108' },
            { type: 'placeholder', id: '118' },
            { type: 'placeholder', id: '128' }
          ],
          mergePass: true,
          mergeResult: [
            {
              id: '128',
              type: 'placeholder',
              AD_Config_group: 'panel_pod_artykulem',
              AD_Config_element_id: '9'
            }
          ]
        },
        {
          ruleName: 'banerDetalX',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '46' }],
          mergePass: true,
          mergeResult: [
            {
              id: '46',
              type: 'placeholder',
              AD_Config_group: 'baner_detal',
              AD_Config_element_id: '2'
            }
          ]
        },
        {
          ruleName: 'brandingPlayer',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '12' }],
          mergePass: true,
          mergeResult: [
            {
              id: '12',
              type: 'placeholder',
              AD_Config_group: 'branding_playera_main',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'prawaSzpaltaRelated',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '154' }],
          mergePass: true,
          mergeResult: [
            {
              id: '154',
              type: 'placeholder',
              AD_Config_group: 'prawa_szpalta',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'lewyMargines',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '158' }],
          mergePass: true,
          mergeResult: [
            {
              id: '158',
              type: 'placeholder',
              AD_Config_group: 'lewy_margines',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'adexDetal',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '69' }],
          mergePass: true,
          mergeResult: [
            {
              id: '69',
              type: 'placeholder',
              AD_Config_group: 'adex_detal',
              AD_Config_element_id: '1'
            }
          ]
        }
      ],
      fail: [
        {
          ruleName: 'commercialBreakRuleDzienDobry',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '166' },
            eventAdConfigGroup: 'commercial_break'
          }
        },
        {
          ruleName: 'commercialBreakRuleDzienDobry2',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '166' },
            eventAdConfigGroup: 'commercial_break'
          }
        },
        {
          ruleName: 'underMain',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '166' },
            eventAdConfigGroup: 'layer'
          }
        },
        {
          ruleName: 'commercialBreakRuleDD',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '166' },
            eventAdConfigGroup: 'commercial_break'
          }
        },
        { ruleName: 'lewyMarginesInherited', conditionPass: false }
      ]
    },
    reqBodyType: PageTypeEnum.article,
    fullConfigName: 'story'
  },
  requestMeta: {
    deviceType: DESKTOP,
    locationInfoPageType: 'story_story_video',
    locationInfoSectionId: '222',
    locationInfoSectionName: 'Gwiazdy',
    serviceEnv: 'production',
    serviceId: 'ddtvn',
    siteVersion: 'ab_atsdk_ga',
    time: '1716539400000',
    siteVersionIdentifier: '7931689_20240524074854_222_aa6fa2ed0109e76f090e83ba88bc2d12',
    locationInfoPageId: '7931689'
  },
  masterId: '',
  bgPlugSrc: '',
  activationThresholds: { offset: null, percent: null, delay: 0 },
  trafficCategory: [],
  placeholders: [
    {
      type: 'placeholder',
      id: '2',
      configId: 'on_top',
      enabled: true,
      AD_Config_group: 'on_top',
      AD_Config_element_id: '1',
      deviceType: [DESKTOP],
      width: '980px',
      height: '320px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      code: '',
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '1',
      configId: 'layer',
      enabled: true,
      AD_Config_group: 'layer',
      AD_Config_element_id: '1',
      deviceType: [DESKTOP],
      width: '0px',
      height: '0px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      code: '',
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '23',
      configId: 'baner_detal_1',
      enabled: true,
      AD_Config_group: 'baner_detal',
      AD_Config_element_id: '1',
      deviceType: [DESKTOP],
      width: '750px',
      height: '320px',
      adServer: GAM,
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '46',
      configId: 'baner_detal_2',
      enabled: true,
      AD_Config_group: 'baner_detal',
      AD_Config_element_id: '2',
      deviceType: [DESKTOP],
      width: '750px',
      height: '320px',
      adServer: GAM,
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '74',
      configId: 'panel_pod_1_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '1',
      deviceType: [DESKTOP],
      width: '750px',
      height: '370px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '78',
      configId: 'panel_pod_3_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '2',
      deviceType: [DESKTOP],
      width: '750px',
      height: '370px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '82',
      configId: 'panel_pod_5_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '3',
      deviceType: [DESKTOP],
      width: '750px',
      height: '370px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '86',
      configId: 'panel_pod_7_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '4',
      deviceType: [DESKTOP],
      width: '750px',
      height: '320px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '90',
      configId: 'panel_pod_9_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '5',
      deviceType: [DESKTOP],
      width: '750px',
      height: '320px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '98',
      configId: 'panel_pod_13_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '6',
      deviceType: [DESKTOP],
      width: '750px',
      height: '320px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '108',
      configId: 'panel_pod_18_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '7',
      deviceType: [DESKTOP],
      width: '750px',
      height: '320px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '118',
      configId: 'panel_pod_23_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '8',
      deviceType: [DESKTOP],
      width: '750px',
      height: '320px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '128',
      configId: 'panel_pod_28_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '9',
      deviceType: [DESKTOP],
      width: '750px',
      height: '320px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '12',
      configId: 'branding_playera_main',
      enabled: true,
      AD_Config_group: 'branding_playera_main',
      AD_Config_element_id: '1',
      deviceType: [DESKTOP],
      width: '100%',
      height: '100%',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '154',
      configId: 'prawa_szpalta',
      enabled: true,
      AD_Config_group: 'prawa_szpalta',
      AD_Config_element_id: '1',
      deviceType: [DESKTOP],
      width: '300px',
      height: '620px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '158',
      configId: 'lewy_margines',
      enabled: true,
      AD_Config_group: 'lewy_margines',
      AD_Config_element_id: '1',
      deviceType: [DESKTOP],
      width: '300px',
      height: '620px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '69',
      configId: 'adex_detal',
      enabled: true,
      AD_Config_group: 'adex_detal',
      AD_Config_element_id: '1',
      deviceType: [DESKTOP],
      width: '750px',
      height: '320px',
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    }
  ],
  version: 'release/1.63.0',
  adsConfigIdentificationData: {
    configName: 'story',
    serviceId: ['dd_tvn'],
    rulesPackage: 'original rules (no rules package assigned)',
    releaseVersion: 'release/1.63.0/'
  } as IAdsConfigIdentificationData // independent of appVersion and modifiedDate
};

export const outputMock_AnyFact: IGeneratorResponse = {
  debugData: {
    releaseVersion: 'release/1.63.0/',
    allAvailableAdConfigGroups: [
      'group: lewy_margines, id: 1',
      'group: panel_pod_artykulem, id: 5',
      'group: panel_pod_artykulem, id: 6',
      'group: panel_pod_artykulem, id: 7',
      'group: panel_pod_artykulem, id: 8',
      'group: panel_pod_artykulem, id: 9',
      'group: adex_detal, id: 1',
      'group: layer, id: 1',
      'group: on_top, id: 1',
      'group: branding_playera_main, id: 1',
      'group: baner_detal, id: 1',
      'group: baner_detal, id: 2',
      'group: baner_detal, id: 3',
      'group: baner_detal, id: 4',
      'group: baner_detal, id: 5',
      'group: baner_detal, id: 6',
      'group: baner_detal, id: 7',
      'group: baner_detal, id: 8',
      'group: baner_detal, id: 9',
      'group: baner_detal, id: 10',
      'group: panel_pod_artykulem, id: 1',
      'group: panel_pod_artykulem, id: 2',
      'group: panel_pod_artykulem, id: 3',
      'group: panel_pod_artykulem, id: 4',
      'group: panel_pod_artykulem, id: 4',
      'group: prawa_szpalta, id: 1'
    ],
    allRulesCount: 14,
    successConditionsCount: 13,
    successEventsCount: 13,
    successMergeCount: 9,
    shortSuccessMergeStats: [
      { placeholderId: '2', group: 'on_top', groupId: '1' },
      { placeholderId: '1', group: 'layer', groupId: '1' },
      { placeholderId: '23', group: 'baner_detal', groupId: '1' },
      { placeholderId: '46', group: 'baner_detal', groupId: '2' },
      { placeholderId: '74', group: 'panel_pod_artykulem', groupId: '1' },
      { placeholderId: '78', group: 'panel_pod_artykulem', groupId: '2' },
      { placeholderId: '82', group: 'panel_pod_artykulem', groupId: '3' },
      { placeholderId: '86', group: 'panel_pod_artykulem', groupId: '4' },
      { placeholderId: '90', group: 'panel_pod_artykulem', groupId: '5' },
      { placeholderId: '98', group: 'panel_pod_artykulem', groupId: '6' },
      { placeholderId: '108', group: 'panel_pod_artykulem', groupId: '7' },
      { placeholderId: '118', group: 'panel_pod_artykulem', groupId: '8' },
      { placeholderId: '128', group: 'panel_pod_artykulem', groupId: '9' },
      { placeholderId: '12', group: 'branding_playera_main', groupId: '1' },
      { placeholderId: '154', group: 'prawa_szpalta', groupId: '1' },
      { placeholderId: '158', group: 'lewy_margines', groupId: '1' },
      { placeholderId: '16', group: 'adex_detal', groupId: '1' }
    ],
    rulesStats: {
      success: [
        {
          ruleName: 'onTopZeScreeningiem',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '2' }],
          mergePass: true,
          mergeResult: [
            {
              id: '2',
              type: 'placeholder',
              AD_Config_group: 'on_top',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'onTop2',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '1' }],
          mergePass: true,
          mergeResult: [
            {
              id: '1',
              type: 'placeholder',
              AD_Config_group: 'layer',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'banerDetal1',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '23' }],
          mergePass: true,
          mergeResult: [
            {
              id: '23',
              type: 'placeholder',
              AD_Config_group: 'baner_detal',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'panelPodXArtykulem',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { type: 'placeholder', id: '74' },
            { type: 'placeholder', id: '78' },
            { type: 'placeholder', id: '82' },
            { type: 'placeholder', id: '86' },
            { type: 'placeholder', id: '90' },
            { type: 'placeholder', id: '98' },
            { type: 'placeholder', id: '108' },
            { type: 'placeholder', id: '118' },
            { type: 'placeholder', id: '128' }
          ],
          mergePass: true,
          mergeResult: [
            {
              id: '128',
              type: 'placeholder',
              AD_Config_group: 'panel_pod_artykulem',
              AD_Config_element_id: '9'
            }
          ]
        },
        {
          ruleName: 'banerDetalX',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ id: '46', type: 'placeholder' }],
          mergePass: true,
          mergeResult: [
            {
              id: '46',
              type: 'placeholder',
              AD_Config_group: 'baner_detal',
              AD_Config_element_id: '2'
            }
          ]
        },
        {
          ruleName: 'brandingPlayer',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '12' }],
          mergePass: true,
          mergeResult: [
            {
              id: '12',
              type: 'placeholder',
              AD_Config_group: 'branding_playera_main',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'prawaSzpaltaRelated',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '154' }],
          mergePass: true,
          mergeResult: [
            {
              id: '154',
              type: 'placeholder',
              AD_Config_group: 'prawa_szpalta',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'lewyMargines',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '158' }],
          mergePass: true,
          mergeResult: [
            {
              id: '158',
              type: 'placeholder',
              AD_Config_group: 'lewy_margines',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'adexDetalAnyFact',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '16' }],
          mergePass: true,
          mergeResult: [
            {
              id: '16',
              type: 'placeholder',
              AD_Config_group: 'adex_detal',
              AD_Config_element_id: '1'
            }
          ]
        }
      ],
      fail: [
        {
          ruleName: 'commercialBreakRuleDzienDobry',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '166' },
            eventAdConfigGroup: 'commercial_break'
          }
        },
        {
          ruleName: 'commercialBreakRuleDzienDobry2',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '166' },
            eventAdConfigGroup: 'commercial_break'
          }
        },
        {
          ruleName: 'underMain',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '166' },
            eventAdConfigGroup: 'layer'
          }
        },
        {
          ruleName: 'commercialBreakRuleDD',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '166' },
            eventAdConfigGroup: 'commercial_break'
          }
        },
        { ruleName: 'lewyMarginesInherited', conditionPass: false }
      ]
    },
    reqBodyType: PageTypeEnum.article,
    fullConfigName: 'AnyFact'
  },
  requestMeta: {
    deviceType: TABLET,
    locationInfoPageId: '7931689',
    locationInfoPageType: 'story_story_video',
    locationInfoSectionId: 'AnyFact',
    locationInfoSectionName: 'Gwiazdy',
    serviceEnv: 'production',
    serviceId: 'ddtvn',
    siteVersion: 'ab_atsdk_ga',
    time: '1716539400000',
    siteVersionIdentifier: '7931689_20240524074854_222_aa6fa2ed0109e76f090e83ba88bc2d12'
  },
  masterId: '',
  bgPlugSrc: '',
  activationThresholds: { offset: null, percent: null, delay: 0 },
  trafficCategory: [],
  placeholders: [
    {
      type: 'placeholder',
      id: '2',
      configId: 'on_top',
      enabled: true,
      AD_Config_group: 'on_top',
      AD_Config_element_id: '1',
      deviceType: [TABLET],
      width: '980px',
      height: '320px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '1',
      configId: 'layer',
      enabled: true,
      AD_Config_group: 'layer',
      AD_Config_element_id: '1',
      deviceType: [TABLET],
      width: '0px',
      height: '0px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '23',
      configId: 'baner_detal_1',
      enabled: true,
      AD_Config_group: 'baner_detal',
      AD_Config_element_id: '1',
      deviceType: [TABLET],
      width: '750px',
      height: '320px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '46',
      type: 'placeholder',
      configId: 'baner_detal_2',
      enabled: true,
      AD_Config_group: 'baner_detal',
      AD_Config_element_id: '2',
      deviceType: [TABLET],
      width: '750px',
      height: '320px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '74',
      configId: 'panel_pod_1_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '1',
      deviceType: [TABLET],
      width: '750px',
      height: '370px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '78',
      configId: 'panel_pod_3_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '2',
      deviceType: [TABLET],
      width: '750px',
      height: '370px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '82',
      configId: 'panel_pod_5_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '3',
      deviceType: [TABLET],
      width: '750px',
      height: '370px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '86',
      configId: 'panel_pod_7_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '4',
      deviceType: [TABLET],
      width: '750px',
      height: '320px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '90',
      configId: 'panel_pod_9_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '5',
      deviceType: [TABLET],
      width: '750px',
      height: '320px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '98',
      configId: 'panel_pod_13_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '6',
      deviceType: [TABLET],
      width: '750px',
      height: '320px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '108',
      configId: 'panel_pod_18_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '7',
      deviceType: [TABLET],
      width: '750px',
      height: '320px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '118',
      configId: 'panel_pod_23_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '8',
      deviceType: [TABLET],
      width: '750px',
      height: '320px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '128',
      configId: 'panel_pod_28_artykulem',
      enabled: true,
      AD_Config_group: 'panel_pod_artykulem',
      AD_Config_element_id: '9',
      deviceType: [TABLET],
      width: '750px',
      height: '320px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '12',
      configId: 'branding_playera_main',
      enabled: true,
      AD_Config_group: 'branding_playera_main',
      AD_Config_element_id: '1',
      deviceType: [TABLET],
      width: '100%',
      height: '100%',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '154',
      configId: 'prawa_szpalta',
      enabled: true,
      AD_Config_group: 'prawa_szpalta',
      AD_Config_element_id: '1',
      deviceType: [TABLET],
      width: '300px',
      height: '620px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '158',
      configId: 'lewy_margines',
      enabled: true,
      AD_Config_group: 'lewy_margines',
      AD_Config_element_id: '1',
      deviceType: [TABLET],
      width: '300px',
      height: '620px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      type: 'placeholder',
      id: '16',
      configId: 'adex_detal',
      enabled: true,
      AD_Config_group: 'adex_detal',
      AD_Config_element_id: '1',
      deviceType: [TABLET],
      width: '750px',
      height: '320px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    }
  ],
  version: 'release/1.63.0',
  adsConfigIdentificationData: {
    configName: 'AnyFact',
    serviceId: ['dd_tvn'],
    modifiedDate: '2024-10-16 13:23:34',
    rulesPackage: 'original rules (no rules package assigned)',
    releaseVersion: 'release/1.63.0/'
  } as IAdsConfigIdentificationData // independent of appVersion and modifiedDate
};

export const outputMock_package: IGeneratorResponse = {
  debugData: {
    releaseVersion: 'release/1.64.4/',
    allAvailableAdConfigGroups: [
      'group: top_premium, id: 1',
      'group: panel, id: 1',
      'group: panel, id: 2',
      'group: panel, id: 3',
      'group: panel, id: 4',
      'group: panel, id: 5',
      'group: panel, id: 6',
      'group: panel, id: 7',
      'group: panel, id: 8',
      'group: panel_in_article, id: 1',
      'group: panel_in_article, id: 2',
      'group: panel_in_article, id: 3',
      'group: panel_in_article, id: 4',
      'group: panel_in_article, id: 5',
      'group: panel_in_article, id: 6',
      'group: panel_in_article, id: 7',
      'group: panel_in_article, id: 8',
      'group: panel_in_article, id: 9',
      'group: panel_in_article, id: 10',
      'group: native, id: 1',
      'group: native, id: 2',
      'group: native, id: 3',
      'group: native, id: 4',
      'group: native, id: 5',
      'group: native, id: 6',
      'group: native, id: 7',
      'group: native, id: 8',
      'group: native, id: 9',
      'group: native, id: 10',
      'group: layer, id: 1'
    ],
    allRulesCount: 18,
    successConditionsCount: 18,
    successEventsCount: 12,
    successMergeCount: 8,
    shortSuccessMergeStats: [
      { placeholderId: '163', group: 'layer', groupId: '1' },
      { placeholderId: '2', group: 'top_premium', groupId: '1' },
      { placeholderId: '116', group: 'panel', groupId: '1' },
      { placeholderId: '128', group: 'panel', groupId: '2' },
      { placeholderId: '138', group: 'panel', groupId: '3' },
      { placeholderId: '145', group: 'panel', groupId: '4' },
      { placeholderId: '124', group: 'native', groupId: '1' },
      { placeholderId: '131', group: 'native', groupId: '2' },
      { placeholderId: '142', group: 'native', groupId: '3' },
      { placeholderId: '20', group: 'panel_in_article', groupId: '1' },
      { placeholderId: '36', group: 'panel_in_article', groupId: '2' },
      { placeholderId: '48', group: 'panel_in_article', groupId: '4' },
      { placeholderId: '54', group: 'panel_in_article', groupId: '7' },
      { placeholderId: '62', group: 'panel_in_article', groupId: '9' },
      { placeholderId: '66', group: 'panel_in_article', groupId: '10' }
    ],
    rulesStats: {
      success: [
        {
          ruleName: 'ruleArticleLayer',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ id: '163', type: 'placeholder' }],
          mergePass: true,
          mergeResult: [
            {
              id: '163',
              type: 'placeholder',
              AD_Config_group: 'layer',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'topPremiumGeneralRule',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ id: '2', type: 'placeholder' }],
          mergePass: true,
          mergeResult: [
            {
              id: '2',
              type: 'placeholder',
              AD_Config_group: 'top_premium',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'panel1to10DetalK2Rule1',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ id: '128', type: 'placeholder' }],
          mergePass: true,
          mergeResult: [
            {
              id: '128',
              type: 'placeholder',
              AD_Config_group: 'panel',
              AD_Config_element_id: '2'
            }
          ]
        },
        {
          ruleName: 'native1to10DetalK2Rule1',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '124', type: 'placeholder' },
            { id: '131', type: 'placeholder' }
          ],
          mergePass: true,
          mergeResult: [
            {
              id: '131',
              type: 'placeholder',
              AD_Config_group: 'native',
              AD_Config_element_id: '2'
            }
          ]
        },
        {
          ruleName: 'native1to10DetalK2Rule2',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ id: '142', type: 'placeholder' }],
          mergePass: true,
          mergeResult: [
            {
              id: '142',
              type: 'placeholder',
              AD_Config_group: 'native',
              AD_Config_element_id: '3'
            }
          ]
        },
        {
          ruleName: 'panel1to10DetalK2Rule2',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '138', type: 'placeholder' },
            { id: '145', type: 'placeholder' }
          ],
          mergePass: true,
          mergeResult: [
            {
              id: '145',
              type: 'placeholder',
              AD_Config_group: 'panel',
              AD_Config_element_id: '4'
            }
          ]
        },
        {
          ruleName: 'recommendationQueuePanel1Rule',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ id: '116', type: 'placeholder' }],
          mergePass: true,
          mergeResult: [
            {
              id: '116',
              type: 'placeholder',
              AD_Config_group: 'panel',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'panelInArticle1MobileRule',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ id: '20', type: 'placeholder' }],
          mergePass: true,
          mergeResult: [
            {
              id: '20',
              type: 'placeholder',
              AD_Config_group: 'panel_in_article',
              AD_Config_element_id: '1'
            }
          ]
        }
      ],
      fail: [
        {
          ruleName: 'ruleArticleCommercialBreak',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ id: '161', type: 'placeholder' }],
          mergePass: false,
          mergeResult: {
            placeholders: { id: '161', type: 'placeholder' },
            eventAdConfigGroup: 'commercial_break'
          }
        },
        {
          ruleName: 'native1to10DetalK2Rule3',
          conditionPass: true,
          eventPass: false,
          selectedPlaceholders: []
        },
        {
          ruleName: 'native1to10DetalK2Rule4',
          conditionPass: true,
          eventPass: false,
          selectedPlaceholders: []
        },
        {
          ruleName: 'native1to10DetalK2Rule5',
          conditionPass: true,
          eventPass: false,
          selectedPlaceholders: []
        },
        {
          ruleName: 'panel1to10DetalK2Rule4',
          conditionPass: true,
          eventPass: false,
          selectedPlaceholders: []
        },
        {
          ruleName: 'panel1to10DetalK2Rule3',
          conditionPass: true,
          eventPass: false,
          selectedPlaceholders: []
        },
        {
          ruleName: 'panel1to10DetalK2Rule5',
          conditionPass: true,
          eventPass: false,
          selectedPlaceholders: []
        },
        {
          ruleName: 'panelInArticleXMobileRule',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '36', type: 'placeholder' },
            { id: '48', type: 'placeholder' },
            { id: '54', type: 'placeholder' },
            { id: '66', type: 'placeholder' },
            { id: '81', type: 'placeholder' },
            { id: '93', type: 'placeholder' },
            { id: '105', type: 'placeholder' }
          ],
          mergePass: false,
          mergeResult: {
            placeholders: { id: '105', type: 'placeholder' },
            eventAdConfigGroup: 'panel_in_article'
          }
        },
        {
          ruleName: 'panelInArticleXMobileRuleSiteVersion',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '36', type: 'placeholder' },
            { id: '48', type: 'placeholder' },
            { id: '54', type: 'placeholder' },
            { id: '66', type: 'placeholder' },
            { id: '81', type: 'placeholder' },
            { id: '93', type: 'placeholder' },
            { id: '105', type: 'placeholder' }
          ],
          mergePass: false,
          mergeResult: {
            placeholders: { id: '105', type: 'placeholder' },
            eventAdConfigGroup: 'panel_in_article'
          }
        },
        {
          ruleName: 'panelInArticleXMobileRuleV2',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '48', type: 'placeholder' },
            { id: '62', type: 'placeholder' },
            { id: '81', type: 'placeholder' },
            { id: '89', type: 'placeholder' },
            { id: '95', type: 'placeholder' },
            { id: '101', type: 'placeholder' },
            { id: '107', type: 'placeholder' }
          ],
          mergePass: false,
          mergeResult: {
            placeholders: { id: '107', type: 'placeholder' },
            eventAdConfigGroup: 'panel_in_article'
          }
        }
      ]
    },
    reqBodyType: PageTypeEnum.article,
    fullConfigName: 'story'
  },
  requestMeta: {
    deviceType: SMARTPHONE,
    locationInfoPageId: '5780',
    locationInfoPageType: 'story_story_video',
    locationInfoSectionId: '773',
    locationInfoSectionName: 'Śródmieście',
    serviceEnv: 'prev-release-5-22-0',
    serviceId: 'warszawa_k2',
    siteVersion: 'ab_atsdk_gb',
    time: '1729620000000',
    siteVersionIdentifier: '5780_20241015120643_773_9f3310395be0678865aa1bc5c951ed82'
  },
  masterId: '',
  bgPlugSrc: '',
  activationThresholds: { offset: null, percent: null, delay: 0 },
  trafficCategory: [],
  placeholders: [
    {
      id: '163',
      type: 'placeholder',
      configId: 'layer',
      enabled: true,
      AD_Config_group: 'layer',
      AD_Config_element_id: '1',
      deviceType: [SMARTPHONE],
      width: '0px',
      height: '0px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '2',
      type: 'placeholder',
      configId: 'top_premium',
      enabled: true,
      AD_Config_group: 'top_premium',
      AD_Config_element_id: '1',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '60px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '116',
      type: 'placeholder',
      configId: 'panel_1',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '1',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '300px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '128',
      type: 'placeholder',
      configId: 'panel_2',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '2',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '300px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '138',
      type: 'placeholder',
      configId: 'panel_3',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '3',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '300px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '145',
      type: 'placeholder',
      configId: 'panel_4',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '4',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '300px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '124',
      type: 'placeholder',
      configId: 'native_1',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '1',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '100%',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '131',
      type: 'placeholder',
      configId: 'native_2',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '2',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '100%',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '142',
      type: 'placeholder',
      configId: 'native_3',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '3',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '100%',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '20',
      type: 'placeholder',
      configId: 'panel_in_article_1',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '1',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '300px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '36',
      type: 'placeholder',
      configId: 'panel_in_article_2',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '2',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '300px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '48',
      type: 'placeholder',
      configId: 'panel_in_article_4',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '4',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '300px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '54',
      type: 'placeholder',
      configId: 'panel_in_article_7',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '7',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '300px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '62',
      type: 'placeholder',
      configId: 'panel_in_article_9',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '9',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '300px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    },
    {
      id: '66',
      type: 'placeholder',
      configId: 'panel_in_article_10',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '10',
      deviceType: [SMARTPHONE],
      width: '100%',
      height: '300px',
      adServer: GAM,
      code: '',
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      bidders: [],
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 }
    }
  ],
  version: 'release/1.64.4',
  adsConfigIdentificationData: {
    configName: 'story',
    serviceId: ['warszawa_k2'],
    rulesPackage: 'test_package',
    releaseVersion: 'release/1.64.4/'
  } as IAdsConfigIdentificationData // independent of appVersion and modifiedDate
};

export const outputMock_DefaultPriorities: IGeneratorResponse = {
  debugData: {
    releaseVersion: 'release/1.64.4/',
    allAvailableAdConfigGroups: [
      'group: top_premium, id: 1',
      'group: panel, id: 1',
      'group: panel, id: 2',
      'group: panel, id: 3',
      'group: panel, id: 4',
      'group: panel, id: 5',
      'group: panel, id: 6',
      'group: panel, id: 7',
      'group: panel, id: 8',
      'group: panel_in_article, id: 1',
      'group: panel_in_article, id: 2',
      'group: panel_in_article, id: 3',
      'group: panel_in_article, id: 4',
      'group: panel_in_article, id: 5',
      'group: panel_in_article, id: 6',
      'group: panel_in_article, id: 7',
      'group: panel_in_article, id: 8',
      'group: panel_in_article, id: 9',
      'group: panel_in_article, id: 10',
      'group: native, id: 1',
      'group: native, id: 2',
      'group: native, id: 3',
      'group: native, id: 4',
      'group: native, id: 5',
      'group: native, id: 6',
      'group: native, id: 7',
      'group: native, id: 8',
      'group: native, id: 9',
      'group: native, id: 10',
      'group: layer, id: 1'
    ],
    allRulesCount: 8,
    successConditionsCount: 6,
    successEventsCount: 4,
    successMergeCount: 1,
    shortSuccessMergeStats: [
      { placeholderId: '132', group: 'panel', groupId: '1' },
      { placeholderId: '147', group: 'panel', groupId: '2' },
      { placeholderId: '40', group: 'panel_in_article', groupId: '1' },
      { placeholderId: '48', group: 'panel_in_article', groupId: '3' },
      { placeholderId: '52', group: 'panel_in_article', groupId: '4' },
      { placeholderId: '62', group: 'panel_in_article', groupId: '6' },
      { placeholderId: '66', group: 'panel_in_article', groupId: '7' },
      { placeholderId: '81', group: 'panel_in_article', groupId: '9' },
      { placeholderId: '85', group: 'panel_in_article', groupId: '10' }
    ],
    rulesStats: {
      success: [
        {
          ruleName: 'panel2to6DetalK2Rule',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '132', type: 'placeholder' },
            { id: '147', type: 'placeholder' }
          ],
          mergePass: true,
          mergeResult: [
            {
              id: '147',
              type: 'placeholder',
              AD_Config_group: 'panel',
              AD_Config_element_id: '2'
            }
          ]
        }
      ],
      fail: [
        { ruleName: 'panelInArticle1Rule', conditionPass: false },
        { ruleName: 'halfpage1ColumnNarrowRule', conditionPass: false },
        {
          ruleName: 'halfpage2to6DetalRule',
          conditionPass: true,
          eventPass: false,
          selectedPlaceholders: []
        },
        {
          ruleName: 'native1to10K2DetalRule',
          conditionPass: true,
          eventPass: false,
          selectedPlaceholders: []
        },
        {
          ruleName: 'panelInArticleXRuleSiteVersion',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '40', type: 'placeholder' },
            { id: '52', type: 'placeholder' },
            { id: '66', type: 'placeholder' },
            { id: '85', type: 'placeholder' },
            { id: '101', type: 'placeholder' }
          ],
          mergePass: false,
          mergeResult: {
            placeholders: { id: '101', type: 'placeholder' },
            eventAdConfigGroup: 'panel_in_article'
          }
        },
        {
          ruleName: 'panelInArticleXRuleV2',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '48', type: 'placeholder' },
            { id: '62', type: 'placeholder' },
            { id: '81', type: 'placeholder' },
            { id: '89', type: 'placeholder' },
            { id: '101', type: 'placeholder' }
          ],
          mergePass: false,
          mergeResult: {
            placeholders: { id: '101', type: 'placeholder' },
            eventAdConfigGroup: 'panel_in_article'
          }
        },
        {
          ruleName: 'panelInArticleXRule',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '40', type: 'placeholder' },
            { id: '52', type: 'placeholder' },
            { id: '66', type: 'placeholder' },
            { id: '85', type: 'placeholder' },
            { id: '101', type: 'placeholder' }
          ],
          mergePass: false,
          mergeResult: {
            placeholders: { id: '101', type: 'placeholder' },
            eventAdConfigGroup: 'panel_in_article'
          }
        }
      ]
    },
    reqBodyType: PageTypeEnum.article,
    fullConfigName: 'story'
  },
  requestMeta: {
    deviceType: DESKTOP,
    locationInfoPageId: '5780',
    locationInfoPageType: 'story_story_video',
    locationInfoSectionId: '773',
    locationInfoSectionName: 'Śródmieście',
    serviceEnv: 'prev-release-5-22-0',
    serviceId: 'warszawa_k2',
    siteVersion: 'ab_atsdk_gb',
    time: '1729620000000',
    siteVersionIdentifier: '5780_20241015120643_773_9f3310395be0678865aa1bc5c951ed82'
  },
  masterId: '',
  bgPlugSrc: '',
  activationThresholds: { offset: null, percent: null, delay: 0 },
  trafficCategory: [],
  placeholders: [
    {
      id: '132',
      type: 'placeholder',
      configId: 'panel_1',
      enabled: true,
      deviceType: [DESKTOP],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel',
      AD_Config_element_id: '1',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '147',
      type: 'placeholder',
      configId: 'panel_2',
      enabled: true,
      deviceType: [DESKTOP],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel',
      AD_Config_element_id: '2',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '40',
      type: 'placeholder',
      configId: 'panel_in_article_1',
      enabled: true,
      deviceType: [DESKTOP],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '1',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '48',
      type: 'placeholder',
      configId: 'panel_in_article_3',
      enabled: true,
      deviceType: [DESKTOP],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '3',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '620px'
    },
    {
      id: '52',
      type: 'placeholder',
      configId: 'panel_in_article_4',
      enabled: true,
      deviceType: [DESKTOP],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '4',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '62',
      type: 'placeholder',
      configId: 'panel_in_article_6',
      enabled: true,
      deviceType: [DESKTOP],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '6',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '66',
      type: 'placeholder',
      configId: 'panel_in_article_7',
      enabled: true,
      deviceType: [DESKTOP],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '7',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '81',
      type: 'placeholder',
      configId: 'panel_in_article_9',
      enabled: true,
      deviceType: [DESKTOP],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '9',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '85',
      type: 'placeholder',
      configId: 'panel_in_article_10',
      enabled: true,
      deviceType: [DESKTOP],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '10',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    }
  ],
  version: 'release/1.64.4',
  adsConfigIdentificationData: {
    configName: 'story',
    serviceId: ['warszawa_k2'],
    rulesPackage: 'test_package',
    releaseVersion: 'release/1.64.4/'
  } as IAdsConfigIdentificationData // independent of appVersion and modifiedDate
};

export const outputMock_ModifiedPriorities: IGeneratorResponse = {
  debugData: {
    releaseVersion: 'release/1.64.4/',
    allAvailableAdConfigGroups: [
      'group: top_premium, id: 1',
      'group: panel, id: 1',
      'group: panel, id: 2',
      'group: panel, id: 3',
      'group: panel, id: 4',
      'group: panel, id: 5',
      'group: panel, id: 6',
      'group: panel, id: 7',
      'group: panel, id: 8',
      'group: panel_in_article, id: 1',
      'group: panel_in_article, id: 2',
      'group: panel_in_article, id: 3',
      'group: panel_in_article, id: 4',
      'group: panel_in_article, id: 5',
      'group: panel_in_article, id: 6',
      'group: panel_in_article, id: 7',
      'group: panel_in_article, id: 8',
      'group: panel_in_article, id: 9',
      'group: panel_in_article, id: 10',
      'group: native, id: 1',
      'group: native, id: 2',
      'group: native, id: 3',
      'group: native, id: 4',
      'group: native, id: 5',
      'group: native, id: 6',
      'group: native, id: 7',
      'group: native, id: 8',
      'group: native, id: 9',
      'group: native, id: 10',
      'group: layer, id: 1'
    ],
    allRulesCount: 8,
    successConditionsCount: 6,
    successEventsCount: 4,
    successMergeCount: 1,
    shortSuccessMergeStats: [
      { placeholderId: '132', group: 'panel', groupId: '1' },
      { placeholderId: '147', group: 'panel', groupId: '2' },
      { placeholderId: '40', group: 'panel_in_article', groupId: '2' },
      { placeholderId: '48', group: 'panel_in_article', groupId: '3' },
      { placeholderId: '52', group: 'panel_in_article', groupId: '5' },
      { placeholderId: '62', group: 'panel_in_article', groupId: '6' },
      { placeholderId: '66', group: 'panel_in_article', groupId: '8' },
      { placeholderId: '81', group: 'panel_in_article', groupId: '9' },
      { placeholderId: '85', group: 'panel_in_article', groupId: '10' }
    ],
    rulesStats: {
      success: [
        {
          ruleName: 'panel2to6DetalK2RuleTablet',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '132', type: 'placeholder' },
            { id: '147', type: 'placeholder' }
          ],
          mergePass: true,
          mergeResult: [
            {
              id: '147',
              type: 'placeholder',
              AD_Config_group: 'panel',
              AD_Config_element_id: '2'
            }
          ]
        }
      ],
      fail: [
        {
          ruleName: 'halfpage2to6DetalRuleTablet',
          conditionPass: true,
          eventPass: false,
          selectedPlaceholders: []
        },
        { ruleName: 'panelInArticle1RuleTablet', conditionPass: false },
        {
          ruleName: 'panelInArticleXRuleV2Tablet',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '48', type: 'placeholder' },
            { id: '62', type: 'placeholder' },
            { id: '81', type: 'placeholder' },
            { id: '89', type: 'placeholder' },
            { id: '101', type: 'placeholder' }
          ],
          mergePass: false,
          mergeResult: {
            placeholders: { id: '101', type: 'placeholder' },
            eventAdConfigGroup: 'panel_in_article'
          }
        },
        {
          ruleName: 'panelInArticleXRuleTablet',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '40', type: 'placeholder' },
            { id: '52', type: 'placeholder' },
            { id: '66', type: 'placeholder' },
            { id: '85', type: 'placeholder' },
            { id: '101', type: 'placeholder' }
          ],
          mergePass: false,
          mergeResult: {
            placeholders: { id: '101', type: 'placeholder' },
            eventAdConfigGroup: 'panel_in_article'
          }
        },
        { ruleName: 'halfpage1ColumnNarrowRuleTablet', conditionPass: false },
        {
          ruleName: 'native1to10K2DetalRuleTablet',
          conditionPass: true,
          eventPass: false,
          selectedPlaceholders: []
        },
        {
          ruleName: 'panelInArticleXRuleSiteVersionTablet',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { id: '40', type: 'placeholder' },
            { id: '52', type: 'placeholder' },
            { id: '66', type: 'placeholder' },
            { id: '85', type: 'placeholder' },
            { id: '101', type: 'placeholder' }
          ],
          mergePass: false,
          mergeResult: {
            placeholders: { id: '101', type: 'placeholder' },
            eventAdConfigGroup: 'panel_in_article'
          }
        }
      ]
    },
    reqBodyType: PageTypeEnum.article,
    fullConfigName: 'story'
  },
  requestMeta: {
    deviceType: TABLET,
    locationInfoPageId: '5780',
    locationInfoPageType: 'story_story_video',
    locationInfoSectionId: '773',
    locationInfoSectionName: 'Śródmieście',
    serviceEnv: 'prev-release-5-22-0',
    serviceId: 'warszawa_k2',
    siteVersion: 'ab_atsdk_gb',
    time: '1729620000000',
    siteVersionIdentifier: '5780_20241015120643_773_9f3310395be0678865aa1bc5c951ed82'
  },
  masterId: '',
  bgPlugSrc: '',
  activationThresholds: { offset: null, percent: null, delay: 0 },
  trafficCategory: [],
  placeholders: [
    {
      id: '132',
      type: 'placeholder',
      configId: 'panel_1',
      enabled: true,
      deviceType: [TABLET],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel',
      AD_Config_element_id: '1',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '147',
      type: 'placeholder',
      configId: 'panel_2',
      enabled: true,
      deviceType: [TABLET],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel',
      AD_Config_element_id: '2',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '40',
      type: 'placeholder',
      configId: 'panel_in_article_2',
      enabled: true,
      deviceType: [TABLET],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '2',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '48',
      type: 'placeholder',
      configId: 'panel_in_article_3',
      enabled: true,
      deviceType: [TABLET],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '3',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '620px'
    },
    {
      id: '52',
      type: 'placeholder',
      configId: 'panel_in_article_5',
      enabled: true,
      deviceType: [TABLET],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '5',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '62',
      type: 'placeholder',
      configId: 'panel_in_article_6',
      enabled: true,
      deviceType: [TABLET],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '6',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '66',
      type: 'placeholder',
      configId: 'panel_in_article_8',
      enabled: true,
      deviceType: [TABLET],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '8',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '81',
      type: 'placeholder',
      configId: 'panel_in_article_9',
      enabled: true,
      deviceType: [TABLET],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '9',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    },
    {
      id: '85',
      type: 'placeholder',
      configId: 'panel_in_article_10',
      enabled: true,
      deviceType: [TABLET],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, bidders: [], height: null },
        { adServer: GAM, bidders: [], height: null }
      ],
      code: '',
      bidders: [],
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '10',
      mediaTypes: { banner: [] },
      activationThresholds: { offset: null, percent: null, delay: 0 },
      width: '100%',
      height: '300px'
    }
  ],
  version: 'release/1.64.4',
  adsConfigIdentificationData: {
    configName: 'story',
    serviceId: ['warszawa_k2'],
    rulesPackage: 'test_package',
    releaseVersion: 'release/1.64.4/'
  } as IAdsConfigIdentificationData // independent of appVersion and modifiedDate
};

export const outputMock_mapRedir: IGeneratorResponse = {
  debugData: {
    releaseVersion: 'release/1.68.0/',
    allAvailableAdConfigGroups: [
      'group: top_premium, id: 1',
      'group: panel, id: 1',
      'group: panel, id: 2',
      'group: panel, id: 3',
      'group: panel, id: 4',
      'group: panel, id: 5',
      'group: panel, id: 6',
      'group: panel, id: 7',
      'group: panel, id: 8',
      'group: panel_in_article, id: 1',
      'group: panel_in_article, id: 2',
      'group: panel_in_article, id: 3',
      'group: panel_in_article, id: 4',
      'group: panel_in_article, id: 5',
      'group: panel_in_article, id: 6',
      'group: panel_in_article, id: 7',
      'group: panel_in_article, id: 8',
      'group: panel_in_article, id: 9',
      'group: panel_in_article, id: 10',
      'group: native, id: 1',
      'group: native, id: 2',
      'group: native, id: 3',
      'group: native, id: 4',
      'group: native, id: 5',
      'group: native, id: 6',
      'group: native, id: 7',
      'group: native, id: 8',
      'group: native, id: 9',
      'group: native, id: 10',
      'group: halfpage, id: 1',
      'group: halfpage, id: 2',
      'group: halfpage, id: 3',
      'group: halfpage, id: 4',
      'group: halfpage, id: 5',
      'group: halfpage, id: 6',
      'group: layer, id: 1',
      'group: commercial_break, id: 1'
    ],
    allRulesCount: 14,
    successConditionsCount: 13,
    successEventsCount: 13,
    successMergeCount: 2,
    shortSuccessMergeStats: [
      { placeholderId: '166', group: 'commercial_break', groupId: '1' },
      { placeholderId: '1', group: 'layer', groupId: '1' }
    ],
    rulesStats: {
      success: [
        {
          ruleName: 'commercialBreakRuleDzienDobry',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: true,
          mergeResult: [
            {
              id: '166',
              type: 'placeholder',
              AD_Config_group: 'commercial_break',
              AD_Config_element_id: '1'
            }
          ]
        },
        {
          ruleName: 'onTop2',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '1' }],
          mergePass: true,
          mergeResult: [
            {
              id: '1',
              type: 'placeholder',
              AD_Config_group: 'layer',
              AD_Config_element_id: '1'
            }
          ]
        }
      ],
      fail: [
        {
          ruleName: 'onTopZeScreeningiem',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '2' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '2' },
            eventAdConfigGroup: 'on_top'
          }
        },
        {
          ruleName: 'commercialBreakRuleDzienDobry2',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '166' },
            eventAdConfigGroup: 'commercial_break'
          }
        },
        {
          ruleName: 'underMain',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '166' },
            eventAdConfigGroup: 'layer'
          }
        },
        {
          ruleName: 'commercialBreakRuleDD',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '166' },
            eventAdConfigGroup: 'commercial_break'
          }
        },
        {
          ruleName: 'banerDetal1',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '23' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '23' },
            eventAdConfigGroup: 'baner_detal'
          }
        },
        {
          ruleName: 'panelPodXArtykulem',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [
            { type: 'placeholder', id: '74' },
            { type: 'placeholder', id: '78' },
            { type: 'placeholder', id: '82' },
            { type: 'placeholder', id: '86' },
            { type: 'placeholder', id: '90' },
            { type: 'placeholder', id: '98' },
            { type: 'placeholder', id: '108' },
            { type: 'placeholder', id: '118' },
            { type: 'placeholder', id: '128' }
          ],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '128' },
            eventAdConfigGroup: 'panel_pod_artykulem'
          }
        },
        {
          ruleName: 'banerDetalX',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ id: '46', type: 'placeholder' }],
          mergePass: false,
          mergeResult: {
            placeholders: { id: '46', type: 'placeholder' },
            eventAdConfigGroup: 'baner_detal'
          }
        },
        {
          ruleName: 'brandingPlayer',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '12' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '12' },
            eventAdConfigGroup: 'branding_playera_main'
          }
        },
        {
          ruleName: 'prawaSzpaltaRelated',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '154' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '154' },
            eventAdConfigGroup: 'prawa_szpalta'
          }
        },
        {
          ruleName: 'lewyMargines',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '158' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '158' },
            eventAdConfigGroup: 'lewy_margines'
          }
        },
        { ruleName: 'lewyMarginesInherited', conditionPass: false },
        {
          ruleName: 'adexDetal',
          conditionPass: true,
          eventPass: true,
          selectedPlaceholders: [{ type: 'placeholder', id: '69' }],
          mergePass: false,
          mergeResult: {
            placeholders: { type: 'placeholder', id: '69' },
            eventAdConfigGroup: 'adex_detal'
          }
        }
      ]
    },
    reqBodyType: PageTypeEnum.article,
    fullConfigName: 'story_story_video'
  },
  requestMeta: {
    deviceType: DESKTOP,
    locationInfoPageId: '7931689',
    locationInfoPageType: 'story_story_video',
    locationInfoSectionId: '222',
    locationInfoSectionName: 'Gwiazdy',
    serviceEnv: 'production',
    serviceId: 'ddtvn',
    siteVersion: 'ab_atsdk_ga',
    time: '1716539400000',
    siteVersionIdentifier: '7931689_20240524074854_222_aa6fa2ed0109e76f090e83ba88bc2d12'
  },
  masterId: '',
  bgPlugSrc: null,
  activationThresholds: { offset: null, percent: null, delay: 0 },
  trafficCategory: ['kobieta', 'manager', 'mezczyzna'],
  placeholders: [
    {
      type: 'placeholder',
      id: '166',
      configId: 'commercial_break',
      enabled: true,
      AD_Config_group: 'commercial_break',
      AD_Config_element_id: '1',
      deviceType: [DESKTOP],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, type: '', slaveId: null, placementId: null },
        {
          adServer: GAM,
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/commercial_break',
          sizes: [[1, 1]],
          kValues: { root: GAM, placeholder: 'commercial_break', slot: 'commercial_break' }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: { sizes: [[1, 1]] } },
      width: '1px',
      height: '1px'
    },
    {
      type: 'placeholder',
      id: '1',
      configId: 'layer',
      enabled: true,
      AD_Config_group: 'layer',
      AD_Config_element_id: '1',
      deviceType: [DESKTOP],
      adServer: GAM,
      adSlots: [
        { adServer: ADOCEAN, type: '', slaveId: null, placementId: null },
        {
          adServer: GAM,
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/layer',
          sizes: [[1, 1]],
          kValues: { root: GAM, placeholder: 'layer', slot: 'layer' }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: { banner: { sizes: [[1, 1]] } },
      width: '0px',
      height: '0px'
    }
  ] as unknown as IRulesPriorities[],
  version: 'release/1.68.0',
  adsConfigIdentificationData: {
    configName: 'story_story_video',
    serviceId: ['warszawa_k2'],
    rulesPackage: 'original rules (no rules package assigned)',
    releaseVersion: 'release/1.68.0/'
  } as IAdsConfigIdentificationData // independent of appVersion and modifiedDate
};

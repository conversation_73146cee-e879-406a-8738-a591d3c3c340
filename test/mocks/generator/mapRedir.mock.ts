export const mapRedirCDNMock = {
  version: 'release/1.68.0',
  items: [
    {
      cfgLocation: 'sdk-display/configs/release/1.68.0/warszawa_k2_20250317172746_0.json',
      pageType: ['main_page', 'main_page_mourning_light'],
      pageId: [],
      section: []
    },
    {
      cfgLocation: 'sdk-display/configs/release/1.68.0/warszawa_k2_20250317172746_1.json',
      pageType: [
        'section_page',
        'section_page_content_hub',
        'section_page_content_hub_mourning_full',
        'section_page_content_hub_mourning_light',
        'section_page_mourning_full',
        'section_page_mourning_light',
        'section_page_section_sponsored',
        'section_page_section_sponsored_mourning_full',
        'section_page_section_sponsored_mourning_light'
      ],
      pageId: [],
      section: []
    },
    {
      cfgLocation: 'sdk-display/configs/release/1.68.0/warszawa_k2_20250317172746_2.json',
      pageType: [
        'main_page_mourning_full',
        'main_page_mourning_full_no_ads',
        'main_page_mourning_light_no_ads',
        'main_page_no_ads',
        'report_mourning_full_no_ads',
        'report_mourning_light_no_ads',
        'report_no_ads',
        'report_report_descriptive_mourning_full_no_ads',
        'report_report_descriptive_mourning_light_no_ads',
        'report_report_descriptive_no_ads',
        'section_page_content_hub_mourning_full_no_ads',
        'section_page_content_hub_mourning_light_no_ads',
        'section_page_content_hub_no_ads',
        'section_page_mourning_full_no_ads',
        'section_page_mourning_light_no_ads',
        'section_page_no_ads',
        'section_page_section_sponsored_mourning_full_no_ads',
        'section_page_section_sponsored_mourning_light_no_ads',
        'section_page_section_sponsored_no_ads',
        'story_content_hub_mourning_full_no_ads',
        'story_content_hub_mourning_light_no_ads',
        'story_content_hub_no_ads',
        'story_mourning_full_no_ads',
        'story_mourning_light_no_ads',
        'story_no_ads',
        'story_section_sponsored_mourning_full_no_ads',
        'story_section_sponsored_mourning_light_no_ads',
        'story_section_sponsored_no_ads',
        'story_story_sponsored_content_hub_mourning_full_no_ads',
        'story_story_sponsored_content_hub_mourning_light_no_ads',
        'story_story_sponsored_content_hub_no_ads',
        'story_story_sponsored_mourning_full_no_ads',
        'story_story_sponsored_mourning_light_no_ads',
        'story_story_sponsored_no_ads',
        'story_story_sponsored_section_sponsored_mourning_full_no_ads',
        'story_story_sponsored_section_sponsored_mourning_light_no_ads',
        'story_story_sponsored_section_sponsored_no_ads',
        'story_story_video_content_hub_mourning_full_no_ads',
        'story_story_video_content_hub_mourning_light_no_ads',
        'story_story_video_content_hub_no_ads',
        'story_story_video_mourning_full_no_ads',
        'story_story_video_mourning_light_no_ads',
        'story_story_video_no_ads',
        'story_story_video_section_sponsored_mourning_full_no_ads',
        'story_story_video_section_sponsored_mourning_light_no_ads',
        'story_story_video_section_sponsored_no_ads',
        'story_story_video_story_sponsored_content_hub_mourning_full_no_ads',
        'story_story_video_story_sponsored_content_hub_mourning_light_no_ads',
        'story_story_video_story_sponsored_content_hub_no_ads',
        'story_story_video_story_sponsored_mourning_full_no_ads',
        'story_story_video_story_sponsored_mourning_light_no_ads',
        'story_story_video_story_sponsored_no_ads',
        'story_story_video_story_sponsored_section_sponsored_mourning_full_no_ads',
        'story_story_video_story_sponsored_section_sponsored_mourning_light_no_ads',
        'story_story_video_story_sponsored_section_sponsored_no_ads',
        'tag_mourning_full_no_ads',
        'tag_mourning_light_no_ads',
        'tag_no_ads'
      ],
      pageId: [],
      section: []
    },
    {
      cfgLocation: 'sdk-display/configs/release/1.68.0/warszawa_k2_20250317172746_3.json',
      pageType: [
        'story',
        'story_content_hub',
        'story_content_hub_mourning_full',
        'story_content_hub_mourning_light',
        'story_mourning_full',
        'story_mourning_light',
        'story_section_sponsored',
        'story_section_sponsored_mourning_full',
        'story_section_sponsored_mourning_light',
        'story_story_sponsored',
        'story_story_sponsored_content_hub',
        'story_story_sponsored_content_hub_mourning_full',
        'story_story_sponsored_content_hub_mourning_light',
        'story_story_sponsored_mourning_full',
        'story_story_sponsored_mourning_light',
        'story_story_sponsored_section_sponsored',
        'story_story_sponsored_section_sponsored_mourning_full',
        'story_story_sponsored_section_sponsored_mourning_light',
        'story_story_video',
        'story_story_video_content_hub',
        'story_story_video_content_hub_mourning_full',
        'story_story_video_content_hub_mourning_light',
        'story_story_video_mourning_full',
        'story_story_video_mourning_light',
        'story_story_video_section_sponsored',
        'story_story_video_section_sponsored_mourning_full',
        'story_story_video_section_sponsored_mourning_light',
        'story_story_video_story_sponsored',
        'story_story_video_story_sponsored_content_hub',
        'story_story_video_story_sponsored_content_hub_mourning_full',
        'story_story_video_story_sponsored_content_hub_mourning_light',
        'story_story_video_story_sponsored_mourning_full',
        'story_story_video_story_sponsored_mourning_light',
        'story_story_video_story_sponsored_section_sponsored',
        'story_story_video_story_sponsored_section_sponsored_mourning_full',
        'story_story_video_story_sponsored_section_sponsored_mourning_light',
        'tag',
        'tag_mourning_full',
        'tag_mourning_light'
      ],
      pageId: [],
      section: []
    },
    {
      cfgLocation: 'sdk-display/configs/release/1.68.0/warszawa_k2_20250317172746_4.json',
      pageType: [
        'report_report_descriptive',
        'report_report_descriptive_mourning_full',
        'report_report_descriptive_mourning_light'
      ],
      pageId: [],
      section: []
    }
  ]
};

export const mapRedirCDNMock2 = {
  masterId: '',
  bgPlugSrc: null,
  activationThresholds: {
    offset: null,
    percent: null,
    delay: 0
  },
  trafficCategory: ['kobieta', 'manager', 'mezczyzna'],
  placeholders: [
    {
      id: 'top_premium',
      enabled: true,
      AD_Config_group: 'top_premium',
      AD_Config_element_id: '1',
      deviceType: ['desktop'],
      width: '100%',
      height: '150px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/top_premium',
          sizes: [[1920, 150]],
          kValues: {
            root: 'gam',
            placeholder: 'top_premium',
            slot: 'top_premium',
            ad_label_enabled: 'false',
            ad_label_outside: 'false'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [[1920, 150]]
        }
      }
    },
    {
      id: 'top_premium',
      enabled: true,
      AD_Config_group: 'top_premium',
      AD_Config_element_id: '1',
      deviceType: ['tablet'],
      width: '100%',
      height: '150px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/top_premium',
          sizes: [[1920, 150]],
          kValues: {
            root: 'gam',
            placeholder: 'top_premium',
            slot: 'top_premium',
            ad_label_enabled: 'false',
            ad_label_outside: 'false'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [[1920, 150]]
        }
      }
    },
    {
      id: 'top_premium',
      enabled: true,
      AD_Config_group: 'top_premium',
      AD_Config_element_id: '1',
      deviceType: ['smartphone'],
      width: '100%',
      height: '60px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/top_premium',
          sizes: [
            [710, 60],
            [768, 60]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'top_premium',
            slot: 'top_premium',
            ad_label_enabled: 'false',
            ad_label_outside: 'false'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [710, 60],
            [768, 60]
          ]
        }
      }
    },
    {
      id: 'panel_1',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '1',
      deviceType: ['desktop'],
      width: '970px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_1',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_1',
            slot: 'panel_1'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_1',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '1',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_1',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_1',
            slot: 'panel_1'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_1',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '1',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_1',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_1',
            slot: 'panel_1'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      }
    },
    {
      id: 'panel_2',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '2',
      deviceType: ['desktop'],
      width: '970px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_2',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_2',
            slot: 'panel_2'
          }
        }
      ],
      code: 'b10',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '1d23c8edf135106a57d7f1694d9c2c7eb67cc326',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127259'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108414',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923847'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_2',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '2',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_2',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_2',
            slot: 'panel_2'
          }
        }
      ],
      code: 'b10',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '1d23c8edf135106a57d7f1694d9c2c7eb67cc326',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127259'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108414',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923851'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_2',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '2',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_2',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_2',
            slot: 'panel_2'
          }
        }
      ],
      code: 'b10',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '1d23c8edf135106a57d7f1694d9c2c7eb67cc326',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127259'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108414',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923851'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      }
    },
    {
      id: 'panel_3',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '3',
      deviceType: ['desktop'],
      width: '970px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_3',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_3',
            slot: 'panel_3'
          }
        }
      ],
      code: 'b11',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '64738e27dc960ebdc22143dd8ea4b16b74029c8a',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127260'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108415',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923848'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_3',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '3',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_3',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_3',
            slot: 'panel_3'
          }
        }
      ],
      code: 'b11',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '64738e27dc960ebdc22143dd8ea4b16b74029c8a',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127260'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108415',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923852'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_3',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '3',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_3',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_3',
            slot: 'panel_3'
          }
        }
      ],
      code: 'b11',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '64738e27dc960ebdc22143dd8ea4b16b74029c8a',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127260'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108415',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923852'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      }
    },
    {
      id: 'panel_4',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '4',
      deviceType: ['desktop'],
      width: '970px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_4',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_4',
            slot: 'panel_4'
          }
        }
      ],
      code: 'b12',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '2c8041adcd90153af5180e4c18e22c3b161c25e8',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127261'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108416',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923849'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_4',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '4',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_4',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_4',
            slot: 'panel_4'
          }
        }
      ],
      code: 'b12',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '2c8041adcd90153af5180e4c18e22c3b161c25e8',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127261'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108416',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923853'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_4',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '4',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_4',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_4',
            slot: 'panel_4'
          }
        }
      ],
      code: 'b12',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '2c8041adcd90153af5180e4c18e22c3b161c25e8',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127261'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108416',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923852'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      }
    },
    {
      id: 'panel_5',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '5',
      deviceType: ['desktop'],
      width: '970px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_5',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_5',
            slot: 'panel_5'
          }
        }
      ],
      code: 'b13',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: 'ad896e3ecf46771003093f24b88cd72af2225023',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127262'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108417',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923850'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_5',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '5',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_5',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_5',
            slot: 'panel_5'
          }
        }
      ],
      code: 'b13',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: 'ad896e3ecf46771003093f24b88cd72af2225023',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127262'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108417',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923854'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_5',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '5',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_5',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_5',
            slot: 'panel_5'
          }
        }
      ],
      code: 'b13',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328179'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: 'ad896e3ecf46771003093f24b88cd72af2225023',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127262'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'panel',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '108417',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '923853'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      }
    },
    {
      id: 'panel_6',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '6',
      deviceType: ['desktop'],
      width: '970px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_6',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_6',
            slot: 'panel_6'
          }
        }
      ],
      code: 'b04',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '506217'
          }
        },
        {
          bidder: 'adocean',
          params: {
            emiter: 'tvn.adocean.pl',
            masterId: 'ClUAkzQ7N51IMCIA9_UuVzMafYJanwe2pymIdPfAPdD.D7',
            slaveId: 'adoceantvnoghigtepui'
          }
        },
        {
          bidder: 'adpone',
          params: {
            placementId: '121528123640157'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: 'aff60c26ab00f0ad980af68c68a926fa11795ef9',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127264'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'banner adex',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '88035',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_6',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '6',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_6',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_6',
            slot: 'panel_6'
          }
        }
      ],
      code: 'b04',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '506217'
          }
        },
        {
          bidder: 'adocean',
          params: {
            emiter: 'tvn.adocean.pl',
            masterId: 'ClUAkzQ7N51IMCIA9_UuVzMafYJanwe2pymIdPfAPdD.D7',
            slaveId: 'adoceantvnoghigtepui'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: 'aff60c26ab00f0ad980af68c68a926fa11795ef9',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127264'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'banner adex',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '88035',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '912745'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_6',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '6',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_6',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_6',
            slot: 'panel_6'
          }
        }
      ],
      code: 'b04',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '506217'
          }
        },
        {
          bidder: 'adocean',
          params: {
            emiter: 'tvn.adocean.pl',
            masterId: 'ClUAkzQ7N51IMCIA9_UuVzMafYJanwe2pymIdPfAPdD.D7',
            slaveId: 'adoceantvnoghigtepui'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: 'aff60c26ab00f0ad980af68c68a926fa11795ef9',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127264'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'banner adex',
            networkId: '3064'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '88035',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '922145'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      }
    },
    {
      id: 'panel_7',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '7',
      deviceType: ['desktop'],
      width: '970px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_7',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_7',
            slot: 'panel_7'
          }
        }
      ],
      code: 'b06',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '506217'
          }
        },
        {
          bidder: 'adocean',
          params: {
            emiter: 'tvn.adocean.pl',
            masterId: 'ClUAkzQ7N51IMCIA9_UuVzMafYJanwe2pymIdPfAPdD.D7',
            slaveId: 'adoceantvnubksfmopkg'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '464f9d1621f0799f4c4b7a2e884b21e4be81d222',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127254'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'banner adex',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '88035',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_7',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '7',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_7',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_7',
            slot: 'panel_7'
          }
        }
      ],
      code: 'b06',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '506217'
          }
        },
        {
          bidder: 'adocean',
          params: {
            emiter: 'tvn.adocean.pl',
            masterId: 'ClUAkzQ7N51IMCIA9_UuVzMafYJanwe2pymIdPfAPdD.D7',
            slaveId: 'adoceantvnubksfmopkg'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '464f9d1621f0799f4c4b7a2e884b21e4be81d222',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127254'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'banner adex',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '88035',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_7',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '7',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_7',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_7',
            slot: 'panel_7'
          }
        }
      ],
      code: 'b06',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '506217'
          }
        },
        {
          bidder: 'adocean',
          params: {
            emiter: 'tvn.adocean.pl',
            masterId: 'ClUAkzQ7N51IMCIA9_UuVzMafYJanwe2pymIdPfAPdD.D7',
            slaveId: 'adoceantvnubksfmopkg'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '464f9d1621f0799f4c4b7a2e884b21e4be81d222',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127254'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'banner adex',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '88035',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      }
    },
    {
      id: 'panel_8',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '8',
      deviceType: ['desktop'],
      width: '970px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_8',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_8',
            slot: 'panel_8'
          }
        }
      ],
      code: 'b07',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '506217'
          }
        },
        {
          bidder: 'adocean',
          params: {
            emiter: 'tvn.adocean.pl',
            masterId: 'ClUAkzQ7N51IMCIA9_UuVzMafYJanwe2pymIdPfAPdD.D7',
            slaveId: 'adoceantvnrcrijjiufb'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '0de7f57bd4db22d7e4a43004aea93b1f0a484259',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127255'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'banner adex',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '88035',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_8',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '8',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_8',
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_8',
            slot: 'panel_8'
          }
        }
      ],
      code: 'b07',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '506217'
          }
        },
        {
          bidder: 'adocean',
          params: {
            emiter: 'tvn.adocean.pl',
            masterId: 'ClUAkzQ7N51IMCIA9_UuVzMafYJanwe2pymIdPfAPdD.D7',
            slaveId: 'adoceantvnrcrijjiufb'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '0de7f57bd4db22d7e4a43004aea93b1f0a484259',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127255'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'banner adex',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '88035',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [930, 180],
            [970, 90],
            [970, 250],
            [750, 100],
            [750, 200]
          ]
        }
      }
    },
    {
      id: 'panel_8',
      enabled: true,
      AD_Config_group: 'panel',
      AD_Config_element_id: '8',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_8',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_8',
            slot: 'panel_8'
          }
        }
      ],
      code: 'b07',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '506217'
          }
        },
        {
          bidder: 'adocean',
          params: {
            emiter: 'tvn.adocean.pl',
            masterId: 'ClUAkzQ7N51IMCIA9_UuVzMafYJanwe2pymIdPfAPdD.D7',
            slaveId: 'adoceantvnrcrijjiufb'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: '0de7f57bd4db22d7e4a43004aea93b1f0a484259',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127255'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'banner adex',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '88035',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      }
    },
    {
      id: 'panel_in_article_1',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '1',
      deviceType: ['desktop'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_1',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_1',
            slot: 'panel_in_article_1'
          }
        }
      ],
      code: 'b15',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328250'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: 'bcceeba1e77189d6a3938f29437de543a18024fb',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127263'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'srodtekst',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '112966',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_1',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '1',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_1',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_1',
            slot: 'panel_in_article_1'
          }
        }
      ],
      code: 'b15',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328250'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: 'bcceeba1e77189d6a3938f29437de543a18024fb',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127263'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'srodtekst',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '112966',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_1',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '1',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_1',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_1',
            slot: 'panel_in_article_1'
          }
        }
      ],
      code: 'b15',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '1328250'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: 'bcceeba1e77189d6a3938f29437de543a18024fb',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127263'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'srodtekst',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '112966',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '925358'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_2',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '2',
      deviceType: ['desktop'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_2',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_2',
            slot: 'panel_in_article_2'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_2',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '2',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_2',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_2',
            slot: 'panel_in_article_2'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_2',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '2',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_2',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_2',
            slot: 'panel_in_article_2'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_3',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '3',
      deviceType: ['desktop'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_3',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_3',
            slot: 'panel_in_article_3'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_3',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '3',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_3',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_3',
            slot: 'panel_in_article_3'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_3',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '3',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_3',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_3',
            slot: 'panel_in_article_3'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_4',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '4',
      deviceType: ['desktop'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_4',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_4',
            slot: 'panel_in_article_4'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_4',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '4',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_4',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_4',
            slot: 'panel_in_article_4'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_4',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '4',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_4',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_4',
            slot: 'panel_in_article_4'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_5',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '5',
      deviceType: ['desktop'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_5',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_5',
            slot: 'panel_in_article_5'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_5',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '5',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_5',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_5',
            slot: 'panel_in_article_5'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_5',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '5',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_5',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_5',
            slot: 'panel_in_article_5'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_6',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '6',
      deviceType: ['desktop'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_6',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_6',
            slot: 'panel_in_article_6'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_6',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '6',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_6',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_6',
            slot: 'panel_in_article_6'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_6',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '6',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_6',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_6',
            slot: 'panel_in_article_6'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_7',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '7',
      deviceType: ['desktop'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_7',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_7',
            slot: 'panel_in_article_7'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_7',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '7',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_7',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_7',
            slot: 'panel_in_article_7'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_7',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '7',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_7',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_7',
            slot: 'panel_in_article_7'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_8',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '8',
      deviceType: ['desktop'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_8',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_8',
            slot: 'panel_in_article_8'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_8',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '8',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_8',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_8',
            slot: 'panel_in_article_8'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_8',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '8',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_8',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_8',
            slot: 'panel_in_article_8'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_9',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '9',
      deviceType: ['desktop'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_9',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_9',
            slot: 'panel_in_article_9'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_9',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '9',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_9',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_9',
            slot: 'panel_in_article_9'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_9',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '9',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_9',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_9',
            slot: 'panel_in_article_9'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_10',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '10',
      deviceType: ['desktop'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_10',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_10',
            slot: 'panel_in_article_10'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_10',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '10',
      deviceType: ['tablet'],
      width: '750px',
      height: '270px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_10',
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_10',
            slot: 'panel_in_article_10'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [468, 60],
            [728, 90],
            [750, 100],
            [750, 200]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'panel_in_article_10',
      enabled: true,
      AD_Config_group: 'panel_in_article',
      AD_Config_element_id: '10',
      deviceType: ['smartphone'],
      width: '100%',
      height: '300px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/panel_in_article_10',
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'panel_in_article_10',
            slot: 'panel_in_article_10'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [300, 250],
            [336, 280],
            [250, 250],
            [200, 200],
            [300, 280],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_1',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '1',
      deviceType: ['desktop'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_1',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_1',
            slot: 'native_1',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_1',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '1',
      deviceType: ['tablet'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_1',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_1',
            slot: 'native_1',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_1',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '1',
      deviceType: ['smartphone'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_1',
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'native_1',
            slot: 'native_1',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_2',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '2',
      deviceType: ['desktop'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_2',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_2',
            slot: 'native_2',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_2',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '2',
      deviceType: ['tablet'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_2',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_2',
            slot: 'native_2',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_2',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '2',
      deviceType: ['smartphone'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_2',
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'native_2',
            slot: 'native_2',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_3',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '3',
      deviceType: ['desktop'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_3',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_3',
            slot: 'native_3',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_3',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '3',
      deviceType: ['tablet'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_3',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_3',
            slot: 'native_3',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_3',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '3',
      deviceType: ['smartphone'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_3',
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'native_3',
            slot: 'native_3',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_4',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '4',
      deviceType: ['desktop'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_4',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_4',
            slot: 'native_4',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_4',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '4',
      deviceType: ['tablet'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_4',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_4',
            slot: 'native_4',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_4',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '4',
      deviceType: ['smartphone'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_4',
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'native_4',
            slot: 'native_4',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_5',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '5',
      deviceType: ['desktop'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_5',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_5',
            slot: 'native_5',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_5',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '5',
      deviceType: ['tablet'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_5',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_5',
            slot: 'native_5',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_5',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '5',
      deviceType: ['smartphone'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_5',
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'native_5',
            slot: 'native_5',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '700px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_6',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '6',
      deviceType: ['desktop'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_6',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_6',
            slot: 'native_6',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_6',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '6',
      deviceType: ['tablet'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_6',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_6',
            slot: 'native_6',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_6',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '6',
      deviceType: ['smartphone'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_6',
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'native_6',
            slot: 'native_6',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_7',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '7',
      deviceType: ['desktop'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_7',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_7',
            slot: 'native_7',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_7',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '7',
      deviceType: ['tablet'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_7',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_7',
            slot: 'native_7',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_7',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '7',
      deviceType: ['smartphone'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_7',
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'native_7',
            slot: 'native_7',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_8',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '8',
      deviceType: ['desktop'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_8',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_8',
            slot: 'native_8',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_8',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '8',
      deviceType: ['tablet'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_8',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_8',
            slot: 'native_8',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_8',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '8',
      deviceType: ['smartphone'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_8',
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'native_8',
            slot: 'native_8',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_9',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '9',
      deviceType: ['desktop'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_9',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_9',
            slot: 'native_9',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_9',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '9',
      deviceType: ['tablet'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_9',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_9',
            slot: 'native_9',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_9',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '9',
      deviceType: ['smartphone'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_9',
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'native_9',
            slot: 'native_9',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_10',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '10',
      deviceType: ['desktop'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_10',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_10',
            slot: 'native_10',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_10',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '10',
      deviceType: ['tablet'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_10',
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]],
          kValues: {
            root: 'gam',
            placeholder: 'native_10',
            slot: 'native_10',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [['fluid'], [300, 250], [336, 280], [250, 250], [200, 200], [300, 280]]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'native_10',
      enabled: true,
      AD_Config_group: 'native',
      AD_Config_element_id: '10',
      deviceType: ['smartphone'],
      width: '100%',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/native_10',
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'native_10',
            slot: 'native_10',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            ['fluid'],
            [300, 60],
            [300, 50],
            [320, 50],
            [320, 100],
            [300, 120],
            [360, 100]
          ]
        }
      },
      activationThresholds: {
        offset: '1200px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'halfpage_1',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '1',
      deviceType: ['desktop'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_1',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_1',
            slot: 'halfpage_1',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: 'b02',
      bidders: [
        {
          bidder: 'adf',
          params: {
            adxDomain: 'adx.adform.net',
            mid: '506215'
          }
        },
        {
          bidder: 'adocean',
          params: {
            emiter: 'tvn.adocean.pl',
            masterId: 'ClUAkzQ7N51IMCIA9_UuVzMafYJanwe2pymIdPfAPdD.D7',
            slaveId: 'adoceantvnxdcfmlhrvt'
          }
        },
        {
          bidder: 'adpone',
          params: {
            placementId: '119728125341555'
          }
        },
        {
          bidder: 'adquery',
          params: {
            placementId: 'a867de00c711504a6b5e881693b5c34e9f0599c5',
            type: 'banner'
          }
        },
        {
          bidder: 'connectad',
          params: {
            networkId: '191',
            siteId: '3127438'
          }
        },
        {
          bidder: 'criteo',
          params: {
            publisherSubId: 'prawa szpalta (PS)',
            networkId: '3064'
          }
        },
        {
          bidder: 'rtbhouse',
          params: {
            region: 'prebid-eu',
            publisherId: 'd41d8cd98f00b204e9'
          }
        },
        {
          bidder: 'smart',
          params: {
            formatId: '88034',
            pageId: '1191315',
            siteId: '340463',
            domain: 'https://prg.smartadserver.com'
          }
        },
        {
          bidder: 'visx',
          params: {
            uid: '922144'
          }
        }
      ],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      }
    },
    {
      id: 'halfpage_1',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '1',
      deviceType: ['tablet'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_1',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_1',
            slot: 'halfpage_1',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: 'b02',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      }
    },
    {
      id: 'halfpage_2',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '2',
      deviceType: ['desktop'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_2',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_2',
            slot: 'halfpage_2',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      }
    },
    {
      id: 'halfpage_2',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '2',
      deviceType: ['tablet'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_2',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_2',
            slot: 'halfpage_2',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      }
    },
    {
      id: 'halfpage_3',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '3',
      deviceType: ['desktop'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_3',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_3',
            slot: 'halfpage_3',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      },
      activationThresholds: {
        offset: '1000px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'halfpage_3',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '3',
      deviceType: ['tablet'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_3',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_3',
            slot: 'halfpage_3',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      },
      activationThresholds: {
        offset: '1000px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'halfpage_4',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '4',
      deviceType: ['desktop'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_4',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_4',
            slot: 'halfpage_4',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      },
      activationThresholds: {
        offset: '1000px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'halfpage_4',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '4',
      deviceType: ['tablet'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_4',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_4',
            slot: 'halfpage_4',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      },
      activationThresholds: {
        offset: '1000px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'halfpage_5',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '5',
      deviceType: ['desktop'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_5',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_5',
            slot: 'halfpage_5',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      },
      activationThresholds: {
        offset: '1000px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'halfpage_5',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '5',
      deviceType: ['tablet'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_5',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_5',
            slot: 'halfpage_5',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      },
      activationThresholds: {
        offset: '1000px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'halfpage_6',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '6',
      deviceType: ['desktop'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_6',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_6',
            slot: 'halfpage_6',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      },
      activationThresholds: {
        offset: '1800px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'halfpage_6',
      enabled: true,
      AD_Config_group: 'halfpage',
      AD_Config_element_id: '6',
      deviceType: ['tablet'],
      width: '300px',
      height: '100%',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/halfpage_6',
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ],
          kValues: {
            root: 'gam',
            placeholder: 'halfpage_6',
            slot: 'halfpage_6',
            ad_label_enabled: 'true',
            ad_label_outside: 'true'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [
            [120, 600],
            [160, 600],
            [300, 600],
            [240, 400],
            [250, 360],
            [300, 250]
          ]
        }
      },
      activationThresholds: {
        offset: '1800px',
        percent: 0,
        delay: 0
      }
    },
    {
      id: 'layer',
      enabled: true,
      AD_Config_group: 'layer',
      AD_Config_element_id: '1',
      deviceType: ['desktop'],
      width: '1px',
      height: '1px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/layer',
          sizes: [[1, 1]],
          kValues: {
            root: 'gam',
            placeholder: 'layer',
            slot: 'layer'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [[1, 1]]
        }
      }
    },
    {
      id: 'layer',
      enabled: true,
      AD_Config_group: 'layer',
      AD_Config_element_id: '1',
      deviceType: ['tablet'],
      width: '1px',
      height: '1px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/layer',
          sizes: [[1, 1]],
          kValues: {
            root: 'gam',
            placeholder: 'layer',
            slot: 'layer'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [[1, 1]]
        }
      }
    },
    {
      id: 'layer',
      enabled: true,
      AD_Config_group: 'layer',
      AD_Config_element_id: '1',
      deviceType: ['smartphone'],
      width: '1px',
      height: '1px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/layer',
          sizes: [[1, 1]],
          kValues: {
            root: 'gam',
            placeholder: 'layer',
            slot: 'layer'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [[1, 1]]
        }
      }
    },
    {
      id: 'commercial_break',
      enabled: true,
      AD_Config_group: 'commercial_break',
      AD_Config_element_id: '1',
      deviceType: ['desktop'],
      width: '1px',
      height: '1px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/commercial_break',
          sizes: [[1, 1]],
          kValues: {
            root: 'gam',
            placeholder: 'commercial_break',
            slot: 'commercial_break'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [[1, 1]]
        }
      }
    },
    {
      id: 'commercial_break',
      enabled: true,
      AD_Config_group: 'commercial_break',
      AD_Config_element_id: '1',
      deviceType: ['tablet'],
      width: '1px',
      height: '1px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/commercial_break',
          sizes: [[1, 1]],
          kValues: {
            root: 'gam',
            placeholder: 'commercial_break',
            slot: 'commercial_break'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [[1, 1]]
        }
      }
    },
    {
      id: 'commercial_break',
      enabled: true,
      AD_Config_group: 'commercial_break',
      AD_Config_element_id: '1',
      deviceType: ['smartphone'],
      width: '1px',
      height: '1px',
      adServer: 'gam',
      adSlots: [
        {
          adServer: 'adocean',
          type: '',
          slaveId: null,
          placementId: null
        },
        {
          adServer: 'gam',
          adUnitPath: '/65073904/11070110/K_TVN_Warszawa/story/commercial_break',
          sizes: [[1, 1]],
          kValues: {
            root: 'gam',
            placeholder: 'commercial_break',
            slot: 'commercial_break'
          }
        }
      ],
      code: '',
      bidders: [],
      mediaTypes: {
        banner: {
          sizes: [[1, 1]]
        }
      }
    }
  ],
  version: 'release/1.68.0'
};

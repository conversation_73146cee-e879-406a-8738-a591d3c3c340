import { PlaceholdersDetails } from 'ads-layouts-tools';

export const placeholderArray_1 = [
  {
    id: 'lewy_margines',
    enabled: true,
    deviceType: ['desktop'],
    width: '300px',
    height: '620px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam', bidders: [] }],
    code: 'b03',
    bidders: [],
    AD_Config_group: 'lewy_margines',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 }
  },
  {
    id: 'panel_pod_9_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b04',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '5',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 }
  },
  {
    id: 'panel_pod_13_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b06',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '6',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 }
  },
  {
    id: 'panel_pod_18_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b07',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '7',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 }
  },
  {
    id: 'panel_pod_23_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b08',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '8',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 }
  },
  {
    id: 'panel_pod_28_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b09',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '9',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'adex_detal',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b14',
    bidders: [],
    AD_Config_group: 'adex_detal',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'layer',
    enabled: true,
    deviceType: ['desktop'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'layer',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'on_top',
    enabled: true,
    deviceType: ['desktop'],
    width: '980px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b01',
    bidders: [],
    AD_Config_group: 'on_top',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'branding_playera_main',
    enabled: true,
    deviceType: ['desktop'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'branding_playera_main',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_1',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b15',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_2',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_3',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_4',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_5',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '5',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_6',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '6',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_7',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '7',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_8',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '8',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_9',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '9',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_10',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '10',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_pod_1_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '370px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b10',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_pod_3_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '370px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b11',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_pod_5_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '370px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b12',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_pod_7_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b13',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'prawa_szpalta',
    enabled: true,
    deviceType: ['desktop'],
    width: '300px',
    height: '620px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b02',
    bidders: [],
    AD_Config_group: 'prawa_szpalta',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  }
] as PlaceholdersDetails[];

export const placeholderArray_2 = [
  {
    id: 'top_premium',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '60px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam', bidders: [] }],
    code: '',
    bidders: [],
    AD_Config_group: 'top_premium',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '60px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_1',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '300px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_2',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b10',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '300px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_3',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b11',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '500px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_4',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b12',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '500px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_5',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b13',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '5',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '500px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_6',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b04',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '6',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '1200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_7',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b06',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '7',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '1200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_8',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b07',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '8',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '1200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_in_article_1',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b15',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_in_article_2',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_in_article_3',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '620px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_in_article_4',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_in_article_5',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '5',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_in_article_6',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '6',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_in_article_7',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '7',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_in_article_8',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '8',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_in_article_9',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '9',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_in_article_10',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '300px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '10',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'native_1',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '300px', percent: 0, delay: 0 }
  },
  {
    id: 'native_2',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '500px', percent: 0, delay: 0 }
  },
  {
    id: 'native_3',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '500px', percent: 0, delay: 0 }
  },
  {
    id: 'native_4',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '600px', percent: 0, delay: 0 }
  },
  {
    id: 'native_5',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '5',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '600px', percent: 0, delay: 0 }
  },
  {
    id: 'native_6',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '6',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '1200px', percent: 0, delay: 0 }
  },
  {
    id: 'native_7',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '7',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '1200px', percent: 0, delay: 0 }
  },
  {
    id: 'native_8',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '8',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '1200px', percent: 0, delay: 0 }
  },
  {
    id: 'native_9',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '9',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '1200px', percent: 0, delay: 0 }
  },
  {
    id: 'native_10',
    enabled: true,
    deviceType: ['smartphone'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '10',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '1200px', percent: 0, delay: 0 }
  },
  {
    id: 'layer',
    enabled: true,
    deviceType: ['smartphone'],
    width: '1px',
    height: '1px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'layer',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '0px', percent: 0, delay: 0 }
  }
] as PlaceholdersDetails[];

export const placeholderArray_3 = [
  {
    id: 'lewy_margines',
    enabled: true,
    deviceType: ['tablet'],
    width: '300px',
    height: '620px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam', bidders: [] }],
    code: 'b03',
    bidders: [],
    AD_Config_group: 'lewy_margines',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 }
  },
  {
    id: 'panel_pod_9_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b04',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '5',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 }
  },
  {
    id: 'panel_pod_13_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b06',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '6',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 }
  },
  {
    id: 'panel_pod_18_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b07',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '7',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 }
  },
  {
    id: 'panel_pod_23_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b08',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '8',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 }
  },
  {
    id: 'panel_pod_28_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b09',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '9',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'adex_detal',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b14',
    bidders: [],
    AD_Config_group: 'adex_detal',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'layer',
    enabled: true,
    deviceType: ['tablet'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'layer',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'on_top',
    enabled: true,
    deviceType: ['tablet'],
    width: '980px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b01',
    bidders: [],
    AD_Config_group: 'on_top',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'branding_playera_main',
    enabled: true,
    deviceType: ['tablet'],
    width: '100%',
    height: '100%',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'branding_playera_main',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_1',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b15',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_2',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_3',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_4',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_5',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '5',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_6',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '6',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_7',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '7',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_8',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '8',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_9',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '9',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'baner_detal_10',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '10',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_pod_1_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '370px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b10',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_pod_3_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '370px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b11',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_pod_5_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '370px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b12',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_pod_7_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b13',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'panel_pod_7_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    width: '750px',
    height: '320px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b13',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  },
  {
    id: 'prawa_szpalta',
    enabled: true,
    deviceType: ['tablet'],
    width: '300px',
    height: '620px',
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b02',
    bidders: [],
    AD_Config_group: 'prawa_szpalta',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 }
  }
] as PlaceholdersDetails[];

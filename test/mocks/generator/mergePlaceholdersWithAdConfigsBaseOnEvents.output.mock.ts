import {
  IMatchedPlaceholdersWithAdConfigs,
  MergePlaceholdersWithAdConfigsBaseOnEventsResult
} from 'InterfacesAndTypes';

export const expectedMergeResult_Empty: MergePlaceholdersWithAdConfigsBaseOnEventsResult = {
  successfulPlaceholdersMerge: [],
  failedPlaceholdersMerge: [
    { type: 'placeholder', id: '2' },
    { type: 'placeholder', id: '166' },
    { type: 'placeholder', id: '166' },
    { type: 'placeholder', id: '166' },
    { type: 'placeholder', id: '1' },
    { type: 'placeholder', id: '166' },
    { type: 'placeholder', id: '23' },
    { type: 'placeholder', id: '46' },
    { type: 'placeholder', id: '74' },
    { type: 'placeholder', id: '78' },
    { type: 'placeholder', id: '82' },
    { type: 'placeholder', id: '86' },
    { type: 'placeholder', id: '90' },
    { type: 'placeholder', id: '98' },
    { type: 'placeholder', id: '108' },
    { type: 'placeholder', id: '118' },
    { type: 'placeholder', id: '128' },
    { type: 'placeholder', id: '12' },
    { type: 'placeholder', id: '154' },
    { type: 'placeholder', id: '158' },
    { type: 'placeholder', id: '69' }
  ],
  rulesStats: [
    {
      ruleName: 'onTopZeScreeningiem',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ type: 'placeholder', id: '2' }],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '2' },
        eventAdConfigGroup: 'on_top'
      }
    },
    {
      ruleName: 'commercialBreakRuleDzienDobry',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '166' },
        eventAdConfigGroup: 'commercial_break'
      }
    },
    {
      ruleName: 'commercialBreakRuleDzienDobry2',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '166' },
        eventAdConfigGroup: 'commercial_break'
      }
    },
    {
      ruleName: 'onTop2',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ type: 'placeholder', id: '1' }],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '1' },
        eventAdConfigGroup: 'layer'
      }
    },
    {
      ruleName: 'underMain',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '166' },
        eventAdConfigGroup: 'layer'
      }
    },
    {
      ruleName: 'commercialBreakRuleDD',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '166' },
        eventAdConfigGroup: 'commercial_break'
      }
    },
    {
      ruleName: 'banerDetal1',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ type: 'placeholder', id: '23' }],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '23' },
        eventAdConfigGroup: 'baner_detal'
      }
    },
    {
      ruleName: 'panelPodXArtykulem',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [
        { type: 'placeholder', id: '74' },
        { type: 'placeholder', id: '78' },
        { type: 'placeholder', id: '82' },
        { type: 'placeholder', id: '86' },
        { type: 'placeholder', id: '90' },
        { type: 'placeholder', id: '98' },
        { type: 'placeholder', id: '108' },
        { type: 'placeholder', id: '118' },
        { type: 'placeholder', id: '128' }
      ],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '128' },
        eventAdConfigGroup: 'panel_pod_artykulem'
      }
    },
    {
      ruleName: 'banerDetalX',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ id: '46', type: 'placeholder' }],
      mergePass: false,
      mergeResult: {
        placeholders: { id: '46', type: 'placeholder' },
        eventAdConfigGroup: 'baner_detal'
      }
    },
    {
      ruleName: 'brandingPlayer',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ type: 'placeholder', id: '12' }],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '12' },
        eventAdConfigGroup: 'branding_playera_main'
      }
    },
    {
      ruleName: 'prawaSzpaltaRelated',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ type: 'placeholder', id: '154' }],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '154' },
        eventAdConfigGroup: 'prawa_szpalta'
      }
    },
    {
      ruleName: 'lewyMargines',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ type: 'placeholder', id: '158' }],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '158' },
        eventAdConfigGroup: 'lewy_margines'
      }
    },
    { ruleName: 'lewyMarginesInherited', conditionPass: false },
    {
      ruleName: 'adexDetal',
      conditionPass: true,
      eventPass: true,
      selectedPlaceholders: [{ type: 'placeholder', id: '69' }],
      mergePass: false,
      mergeResult: {
        placeholders: { type: 'placeholder', id: '69' },
        eventAdConfigGroup: 'adex_detal'
      }
    }
  ]
};

const successfulPlaceholdersMerge_1 = [
  {
    type: 'placeholder',
    id: '2',
    configId: 'on_top',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b01',
    bidders: [],
    AD_Config_group: 'on_top',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '980px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '1',
    configId: 'layer',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'layer',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '0px',
    height: '0px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '23',
    configId: 'baner_detal_1',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b15',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '46',
    type: 'placeholder',
    configId: 'baner_detal_2',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '74',
    configId: 'panel_pod_1_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b10',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '370px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '78',
    configId: 'panel_pod_3_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b11',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '370px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '82',
    configId: 'panel_pod_5_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b12',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '370px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '86',
    configId: 'panel_pod_7_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b13',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '90',
    configId: 'panel_pod_9_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b04',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '5',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '98',
    configId: 'panel_pod_13_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b06',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '6',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '108',
    configId: 'panel_pod_18_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b07',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '7',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '118',
    configId: 'panel_pod_23_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b08',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '8',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '128',
    configId: 'panel_pod_28_artykulem',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b09',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '9',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '12',
    configId: 'branding_playera_main',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'branding_playera_main',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '100%',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '154',
    configId: 'prawa_szpalta',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b02',
    bidders: [],
    AD_Config_group: 'prawa_szpalta',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '300px',
    height: '620px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '158',
    configId: 'lewy_margines',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam', bidders: [] }],
    code: 'b03',
    bidders: [],
    AD_Config_group: 'lewy_margines',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 },
    width: '300px',
    height: '620px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '69',
    configId: 'adex_detal',
    enabled: true,
    deviceType: ['desktop'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b14',
    bidders: [],
    AD_Config_group: 'adex_detal',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  }
] as IMatchedPlaceholdersWithAdConfigs[];
const rulesStats_1 = [
  {
    ruleName: 'onTopZeScreeningiem',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '2' }],
    mergePass: true,
    mergeResult: [
      { id: '2', type: 'placeholder', AD_Config_group: 'on_top', AD_Config_element_id: '1' }
    ]
  },
  {
    ruleName: 'commercialBreakRuleDzienDobry',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
    mergePass: false,
    mergeResult: {
      placeholders: { type: 'placeholder', id: '166' },
      eventAdConfigGroup: 'commercial_break'
    }
  },
  {
    ruleName: 'commercialBreakRuleDzienDobry2',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
    mergePass: false,
    mergeResult: {
      placeholders: { type: 'placeholder', id: '166' },
      eventAdConfigGroup: 'commercial_break'
    }
  },
  {
    ruleName: 'onTop2',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '1' }],
    mergePass: true,
    mergeResult: [
      { id: '1', type: 'placeholder', AD_Config_group: 'layer', AD_Config_element_id: '1' }
    ]
  },
  {
    ruleName: 'underMain',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
    mergePass: false,
    mergeResult: {
      placeholders: { type: 'placeholder', id: '166' },
      eventAdConfigGroup: 'layer'
    }
  },
  {
    ruleName: 'commercialBreakRuleDD',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
    mergePass: false,
    mergeResult: {
      placeholders: { type: 'placeholder', id: '166' },
      eventAdConfigGroup: 'commercial_break'
    }
  },
  {
    ruleName: 'banerDetal1',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '23' }],
    mergePass: true,
    mergeResult: [
      {
        id: '23',
        type: 'placeholder',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '1'
      }
    ]
  },
  {
    ruleName: 'panelPodXArtykulem',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [
      { type: 'placeholder', id: '74' },
      { type: 'placeholder', id: '78' },
      { type: 'placeholder', id: '82' },
      { type: 'placeholder', id: '86' },
      { type: 'placeholder', id: '90' },
      { type: 'placeholder', id: '98' },
      { type: 'placeholder', id: '108' },
      { type: 'placeholder', id: '118' },
      { type: 'placeholder', id: '128' }
    ],
    mergePass: true,
    mergeResult: [
      {
        id: '128',
        type: 'placeholder',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '9'
      }
    ]
  },
  {
    ruleName: 'banerDetalX',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ id: '46', type: 'placeholder' }],
    mergePass: true,
    mergeResult: [
      {
        id: '46',
        type: 'placeholder',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '2'
      }
    ]
  },
  {
    ruleName: 'brandingPlayer',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '12' }],
    mergePass: true,
    mergeResult: [
      {
        id: '12',
        type: 'placeholder',
        AD_Config_group: 'branding_playera_main',
        AD_Config_element_id: '1'
      }
    ]
  },
  {
    ruleName: 'prawaSzpaltaRelated',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '154' }],
    mergePass: true,
    mergeResult: [
      {
        id: '154',
        type: 'placeholder',
        AD_Config_group: 'prawa_szpalta',
        AD_Config_element_id: '1'
      }
    ]
  },
  {
    ruleName: 'lewyMargines',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '158' }],
    mergePass: true,
    mergeResult: [
      {
        id: '158',
        type: 'placeholder',
        AD_Config_group: 'lewy_margines',
        AD_Config_element_id: '1'
      }
    ]
  },
  { ruleName: 'lewyMarginesInherited', conditionPass: false },
  {
    ruleName: 'adexDetal',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '69' }],
    mergePass: true,
    mergeResult: [
      {
        id: '69',
        type: 'placeholder',
        AD_Config_group: 'adex_detal',
        AD_Config_element_id: '1'
      }
    ]
  }
];
export const expectedMergeResult_1: MergePlaceholdersWithAdConfigsBaseOnEventsResult = {
  successfulPlaceholdersMerge: successfulPlaceholdersMerge_1,
  failedPlaceholdersMerge: [
    { type: 'placeholder', id: '166' },
    { type: 'placeholder', id: '166' },
    { type: 'placeholder', id: '166' },
    { type: 'placeholder', id: '166' }
  ],
  rulesStats: rulesStats_1
};

const successfulPlaceholdersMerge_2 = [
  {
    id: '163',
    type: 'placeholder',
    configId: 'layer',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'layer',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '0px', percent: 0, delay: 0 },
    width: '0px',
    height: '0px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '2',
    type: 'placeholder',
    configId: 'top_premium',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam', bidders: [] }],
    code: '',
    bidders: [],
    AD_Config_group: 'top_premium',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '60px', percent: 0, delay: 0 },
    width: '100%',
    height: '60px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '116',
    type: 'placeholder',
    configId: 'panel_1',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '300px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '128',
    type: 'placeholder',
    configId: 'panel_2',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b10',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '300px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '138',
    type: 'placeholder',
    configId: 'panel_3',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b11',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '500px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '145',
    type: 'placeholder',
    configId: 'panel_4',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b12',
    bidders: [],
    AD_Config_group: 'panel',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '500px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '124',
    type: 'placeholder',
    configId: 'native_1',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '300px', percent: 0, delay: 0 },
    width: '100%',
    height: '100%',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '131',
    type: 'placeholder',
    configId: 'native_2',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '500px', percent: 0, delay: 0 },
    width: '100%',
    height: '100%',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '142',
    type: 'placeholder',
    configId: 'native_3',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'native',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '500px', percent: 0, delay: 0 },
    width: '100%',
    height: '100%',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '20',
    type: 'placeholder',
    configId: 'panel_in_article_1',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b15',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '36',
    type: 'placeholder',
    configId: 'panel_in_article_2',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '36',
    type: 'placeholder',
    configId: 'panel_in_article_3',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '620px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '48',
    type: 'placeholder',
    configId: 'panel_in_article_4',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '48',
    type: 'placeholder',
    configId: 'panel_in_article_5',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '5',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '48',
    type: 'placeholder',
    configId: 'panel_in_article_6',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '6',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '54',
    type: 'placeholder',
    configId: 'panel_in_article_7',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '7',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '54',
    type: 'placeholder',
    configId: 'panel_in_article_8',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '8',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '62',
    type: 'placeholder',
    configId: 'panel_in_article_9',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '9',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '66',
    type: 'placeholder',
    configId: 'panel_in_article_10',
    enabled: true,
    deviceType: ['smartphone'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '10',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '300px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  }
] as IMatchedPlaceholdersWithAdConfigs[];
const rulesStats_2 = [
  {
    ruleName: 'ruleArticleCommercialBreak',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ id: '161', type: 'placeholder' }],
    mergePass: false,
    mergeResult: {
      placeholders: { id: '161', type: 'placeholder' },
      eventAdConfigGroup: 'commercial_break'
    }
  },
  {
    ruleName: 'ruleArticleLayer',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ id: '163', type: 'placeholder' }],
    mergePass: true,
    mergeResult: [
      { id: '163', type: 'placeholder', AD_Config_group: 'layer', AD_Config_element_id: '1' }
    ]
  },
  {
    ruleName: 'topPremiumGeneralRule',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ id: '2', type: 'placeholder' }],
    mergePass: true,
    mergeResult: [
      {
        id: '2',
        type: 'placeholder',
        AD_Config_group: 'top_premium',
        AD_Config_element_id: '1'
      }
    ]
  },
  {
    ruleName: 'panel1to10DetalK2Rule1',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ id: '128', type: 'placeholder' }],
    mergePass: true,
    mergeResult: [
      { id: '128', type: 'placeholder', AD_Config_group: 'panel', AD_Config_element_id: '2' }
    ]
  },
  {
    ruleName: 'native1to10DetalK2Rule3',
    conditionPass: true,
    eventPass: false,
    selectedPlaceholders: []
  },
  {
    ruleName: 'native1to10DetalK2Rule4',
    conditionPass: true,
    eventPass: false,
    selectedPlaceholders: []
  },
  {
    ruleName: 'native1to10DetalK2Rule5',
    conditionPass: true,
    eventPass: false,
    selectedPlaceholders: []
  },
  {
    ruleName: 'panel1to10DetalK2Rule4',
    conditionPass: true,
    eventPass: false,
    selectedPlaceholders: []
  },
  {
    ruleName: 'native1to10DetalK2Rule1',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [
      { id: '124', type: 'placeholder' },
      { id: '131', type: 'placeholder' }
    ],
    mergePass: true,
    mergeResult: [
      { id: '131', type: 'placeholder', AD_Config_group: 'native', AD_Config_element_id: '2' }
    ]
  },
  {
    ruleName: 'panel1to10DetalK2Rule3',
    conditionPass: true,
    eventPass: false,
    selectedPlaceholders: []
  },
  {
    ruleName: 'native1to10DetalK2Rule2',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ id: '142', type: 'placeholder' }],
    mergePass: true,
    mergeResult: [
      { id: '142', type: 'placeholder', AD_Config_group: 'native', AD_Config_element_id: '3' }
    ]
  },
  {
    ruleName: 'panel1to10DetalK2Rule2',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [
      { id: '138', type: 'placeholder' },
      { id: '145', type: 'placeholder' }
    ],
    mergePass: true,
    mergeResult: [
      { id: '145', type: 'placeholder', AD_Config_group: 'panel', AD_Config_element_id: '4' }
    ]
  },
  {
    ruleName: 'panel1to10DetalK2Rule5',
    conditionPass: true,
    eventPass: false,
    selectedPlaceholders: []
  },
  {
    ruleName: 'recommendationQueuePanel1Rule',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ id: '116', type: 'placeholder' }],
    mergePass: true,
    mergeResult: [
      { id: '116', type: 'placeholder', AD_Config_group: 'panel', AD_Config_element_id: '1' }
    ]
  },
  {
    ruleName: 'panelInArticle1MobileRule',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ id: '20', type: 'placeholder' }],
    mergePass: true,
    mergeResult: [
      {
        id: '20',
        type: 'placeholder',
        AD_Config_group: 'panel_in_article',
        AD_Config_element_id: '1'
      }
    ]
  },
  {
    ruleName: 'panelInArticleXMobileRule',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [
      { id: '36', type: 'placeholder' },
      { id: '48', type: 'placeholder' },
      { id: '54', type: 'placeholder' },
      { id: '66', type: 'placeholder' },
      { id: '81', type: 'placeholder' },
      { id: '93', type: 'placeholder' },
      { id: '105', type: 'placeholder' }
    ],
    mergePass: false,
    mergeResult: {
      placeholders: { id: '105', type: 'placeholder' },
      eventAdConfigGroup: 'panel_in_article'
    }
  },
  {
    ruleName: 'panelInArticleXMobileRuleSiteVersion',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [
      { id: '36', type: 'placeholder' },
      { id: '48', type: 'placeholder' },
      { id: '54', type: 'placeholder' },
      { id: '66', type: 'placeholder' },
      { id: '81', type: 'placeholder' },
      { id: '93', type: 'placeholder' },
      { id: '105', type: 'placeholder' }
    ],
    mergePass: false,
    mergeResult: {
      placeholders: { id: '105', type: 'placeholder' },
      eventAdConfigGroup: 'panel_in_article'
    }
  },
  {
    ruleName: 'panelInArticleXMobileRuleV2',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [
      { id: '48', type: 'placeholder' },
      { id: '62', type: 'placeholder' },
      { id: '81', type: 'placeholder' },
      { id: '89', type: 'placeholder' },
      { id: '95', type: 'placeholder' },
      { id: '101', type: 'placeholder' },
      { id: '107', type: 'placeholder' }
    ],
    mergePass: false,
    mergeResult: {
      placeholders: { id: '107', type: 'placeholder' },
      eventAdConfigGroup: 'panel_in_article'
    }
  }
];
export const expectedMergeResult_2: MergePlaceholdersWithAdConfigsBaseOnEventsResult = {
  successfulPlaceholdersMerge: successfulPlaceholdersMerge_2,
  failedPlaceholdersMerge: [
    { id: '161', type: 'placeholder' },
    { id: '66', type: 'placeholder' },
    { id: '81', type: 'placeholder' },
    { id: '81', type: 'placeholder' },
    { id: '81', type: 'placeholder' },
    { id: '89', type: 'placeholder' },
    { id: '93', type: 'placeholder' },
    { id: '93', type: 'placeholder' },
    { id: '95', type: 'placeholder' },
    { id: '101', type: 'placeholder' },
    { id: '105', type: 'placeholder' },
    { id: '105', type: 'placeholder' },
    { id: '107', type: 'placeholder' }
  ],
  rulesStats: rulesStats_2
};

const successfulPlaceholdersMerge_3 = [
  {
    type: 'placeholder',
    id: '2',
    configId: 'on_top',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b01',
    bidders: [],
    AD_Config_group: 'on_top',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '980px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '1',
    configId: 'layer',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'layer',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '0px',
    height: '0px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '23',
    configId: 'baner_detal_1',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b15',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    id: '46',
    type: 'placeholder',
    configId: 'baner_detal_2',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'baner_detal',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '74',
    configId: 'panel_pod_1_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b10',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '370px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '78',
    configId: 'panel_pod_3_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b11',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '2',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '370px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '82',
    configId: 'panel_pod_5_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b12',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '3',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '370px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '86',
    configId: 'panel_pod_7_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b13',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '4',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '90',
    configId: 'panel_pod_9_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b04',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '5',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '98',
    configId: 'panel_pod_13_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b06',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '6',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '108',
    configId: 'panel_pod_18_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b07',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '7',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '118',
    configId: 'panel_pod_23_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b08',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '8',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '128',
    configId: 'panel_pod_28_artykulem',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b09',
    bidders: [],
    AD_Config_group: 'panel_pod_artykulem',
    AD_Config_element_id: '9',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '12',
    configId: 'branding_playera_main',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: '',
    bidders: [],
    AD_Config_group: 'branding_playera_main',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '100%',
    height: '100%',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '154',
    configId: 'prawa_szpalta',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b02',
    bidders: [],
    AD_Config_group: 'prawa_szpalta',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '300px',
    height: '620px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '158',
    configId: 'lewy_margines',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam', bidders: [] }],
    code: 'b03',
    bidders: [],
    AD_Config_group: 'lewy_margines',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: null, percent: null, delay: 0 },
    width: '300px',
    height: '620px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  },
  {
    type: 'placeholder',
    id: '16',
    configId: 'adex_detal',
    enabled: true,
    deviceType: ['tablet'],
    adServer: 'gam',
    adSlots: [{ adServer: 'adocean' }, { adServer: 'gam' }],
    code: 'b14',
    bidders: [],
    AD_Config_group: 'adex_detal',
    AD_Config_element_id: '1',
    mediaTypes: { banner: [] },
    activationThresholds: { offset: '200px', percent: 0, delay: 0 },
    width: '750px',
    height: '320px',
    eventPriority: 1,
    rulePriority: 1,
    priorityGroup: undefined
  }
] as IMatchedPlaceholdersWithAdConfigs[];
const rulesStats_3 = [
  {
    ruleName: 'onTopZeScreeningiem',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '2' }],
    mergePass: true,
    mergeResult: [
      { id: '2', type: 'placeholder', AD_Config_group: 'on_top', AD_Config_element_id: '1' }
    ]
  },
  {
    ruleName: 'commercialBreakRuleDzienDobry',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
    mergePass: false,
    mergeResult: {
      placeholders: { type: 'placeholder', id: '166' },
      eventAdConfigGroup: 'commercial_break'
    }
  },
  {
    ruleName: 'commercialBreakRuleDzienDobry2',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
    mergePass: false,
    mergeResult: {
      placeholders: { type: 'placeholder', id: '166' },
      eventAdConfigGroup: 'commercial_break'
    }
  },
  {
    ruleName: 'onTop2',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '1' }],
    mergePass: true,
    mergeResult: [
      { id: '1', type: 'placeholder', AD_Config_group: 'layer', AD_Config_element_id: '1' }
    ]
  },
  {
    ruleName: 'underMain',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
    mergePass: false,
    mergeResult: {
      placeholders: { type: 'placeholder', id: '166' },
      eventAdConfigGroup: 'layer'
    }
  },
  {
    ruleName: 'commercialBreakRuleDD',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '166' }],
    mergePass: false,
    mergeResult: {
      placeholders: { type: 'placeholder', id: '166' },
      eventAdConfigGroup: 'commercial_break'
    }
  },
  {
    ruleName: 'banerDetal1',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '23' }],
    mergePass: true,
    mergeResult: [
      {
        id: '23',
        type: 'placeholder',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '1'
      }
    ]
  },
  {
    ruleName: 'panelPodXArtykulem',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [
      { type: 'placeholder', id: '74' },
      { type: 'placeholder', id: '78' },
      { type: 'placeholder', id: '82' },
      { type: 'placeholder', id: '86' },
      { type: 'placeholder', id: '90' },
      { type: 'placeholder', id: '98' },
      { type: 'placeholder', id: '108' },
      { type: 'placeholder', id: '118' },
      { type: 'placeholder', id: '128' }
    ],
    mergePass: true,
    mergeResult: [
      {
        id: '128',
        type: 'placeholder',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '9'
      }
    ]
  },
  {
    ruleName: 'banerDetalX',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ id: '46', type: 'placeholder' }],
    mergePass: true,
    mergeResult: [
      {
        id: '46',
        type: 'placeholder',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '2'
      }
    ]
  },
  {
    ruleName: 'brandingPlayer',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '12' }],
    mergePass: true,
    mergeResult: [
      {
        id: '12',
        type: 'placeholder',
        AD_Config_group: 'branding_playera_main',
        AD_Config_element_id: '1'
      }
    ]
  },
  {
    ruleName: 'prawaSzpaltaRelated',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '154' }],
    mergePass: true,
    mergeResult: [
      {
        id: '154',
        type: 'placeholder',
        AD_Config_group: 'prawa_szpalta',
        AD_Config_element_id: '1'
      }
    ]
  },
  {
    ruleName: 'lewyMargines',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '158' }],
    mergePass: true,
    mergeResult: [
      {
        id: '158',
        type: 'placeholder',
        AD_Config_group: 'lewy_margines',
        AD_Config_element_id: '1'
      }
    ]
  },
  { ruleName: 'lewyMarginesInherited', conditionPass: false },
  {
    ruleName: 'adexDetalAnyFact',
    conditionPass: true,
    eventPass: true,
    selectedPlaceholders: [{ type: 'placeholder', id: '16' }],
    mergePass: true,
    mergeResult: [
      {
        id: '16',
        type: 'placeholder',
        AD_Config_group: 'adex_detal',
        AD_Config_element_id: '1'
      }
    ]
  }
];
export const expectedMergeResult_3: MergePlaceholdersWithAdConfigsBaseOnEventsResult = {
  successfulPlaceholdersMerge: successfulPlaceholdersMerge_3,
  failedPlaceholdersMerge: [
    { type: 'placeholder', id: '166' },
    { type: 'placeholder', id: '166' },
    { type: 'placeholder', id: '166' },
    { type: 'placeholder', id: '166' }
  ],
  rulesStats: rulesStats_3
};

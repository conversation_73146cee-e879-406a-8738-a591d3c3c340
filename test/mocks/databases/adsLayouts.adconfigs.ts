import { AdConfig, AdConfigDeviceTypeEnum } from 'ads-layouts-tools';
import {
  adConfigCloneFactory,
  adConfigFactory,
  AdConfigFactoryParams,
  PlaceholderForTests
} from 'TestUtils';

const { DESKTOP, SMARTPHONE, TABLET } = AdConfigDeviceTypeEnum;

const ddtvnAdConfigs: AdConfigFactoryParams[] = [
  {
    releaseServices: ['ddtvn'],
    releaseVersion: 'release/1.63.0',
    config_name: 'story',
    pageType: ['story', 'story_story_video'],
    serviceId: ['dd_tvn'],
    placeholders: [
      {
        id: 'lewy_margines',
        AD_Config_group: 'lewy_margines',
        AD_Config_element_id: '1',
        deviceType: [DESKTOP],
        width: '300px',
        height: '620px'
      },
      {
        id: 'panel_pod_9_artykulem',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '5',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'panel_pod_13_artykulem',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '6',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'panel_pod_18_artykulem',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '7',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'panel_pod_23_artykulem',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '8',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'panel_pod_28_artykulem',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '9',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'adex_detal',
        AD_Config_group: 'adex_detal',
        AD_Config_element_id: '1',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'layer',
        AD_Config_group: 'layer',
        AD_Config_element_id: '1',
        deviceType: [DESKTOP],
        width: '100%',
        height: '100%'
      },
      {
        id: 'on_top',
        AD_Config_group: 'on_top',
        AD_Config_element_id: '1',
        deviceType: [DESKTOP],
        width: '980px',
        height: '320px'
      },
      {
        id: 'branding_playera_main',
        AD_Config_group: 'branding_playera_main',
        AD_Config_element_id: '1',
        deviceType: [DESKTOP],
        width: '100%',
        height: '100%'
      },
      {
        id: 'baner_detal_1',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '1',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'baner_detal_2',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '2',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'baner_detal_3',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '3',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'baner_detal_4',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '4',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'baner_detal_5',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '5',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'baner_detal_6',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '6',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'baner_detal_7',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '7',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'baner_detal_8',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '8',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'baner_detal_9',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '9',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'baner_detal_10',
        AD_Config_group: 'baner_detal',
        AD_Config_element_id: '10',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'panel_pod_1_artykulem',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '1',
        deviceType: [DESKTOP],
        width: '750px',
        height: '370px'
      },
      {
        id: 'panel_pod_3_artykulem',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '2',
        deviceType: [DESKTOP],
        width: '750px',
        height: '370px'
      },
      {
        id: 'panel_pod_5_artykulem',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '3',
        deviceType: [DESKTOP],
        width: '750px',
        height: '370px'
      },
      {
        id: 'panel_pod_7_artykulem',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '4',
        deviceType: [DESKTOP],
        width: '750px',
        height: '320px'
      },
      {
        id: 'panel_pod_7_artykulem',
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '4',
        deviceType: [TABLET],
        width: '750px',
        height: '320px'
      },
      {
        id: 'prawa_szpalta',
        AD_Config_group: 'prawa_szpalta',
        AD_Config_element_id: '1',
        deviceType: [DESKTOP],
        width: '300px',
        height: '620px'
      },
      {
        id: 'commercial_break',
        enabled: false,
        AD_Config_group: 'commercial_break',
        AD_Config_element_id: '1',
        deviceType: [SMARTPHONE],
        width: '100%',
        height: '100%'
      }
    ]
  },
  {
    releaseServices: ['ddtvn'],
    releaseVersion: 'release/1.63.0',
    config_name: 'bez_reklam',
    pageType: ['report_no_ads'],
    serviceId: ['ddtvn'],
    placeholders: []
  }
];

ddtvnAdConfigs.push(
  adConfigCloneFactory(ddtvnAdConfigs, 'story', 'AnyFact', {
    placeholder: { deviceType: [TABLET] },
    section: [{ id: 'AnyFact', name: 'AnyFact' }]
  })
);

const warszawa_k2_PlaceholdersSmartphone: PlaceholderForTests[] = [
  {
    id: 'top_premium',
    AD_Config_group: 'top_premium',
    AD_Config_element_id: '1',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '60px'
  },
  {
    id: 'panel_1',
    AD_Config_group: 'panel',
    AD_Config_element_id: '1',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_2',
    AD_Config_group: 'panel',
    AD_Config_element_id: '2',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_3',
    AD_Config_group: 'panel',
    AD_Config_element_id: '3',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_4',
    AD_Config_group: 'panel',
    AD_Config_element_id: '4',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_5',
    AD_Config_group: 'panel',
    AD_Config_element_id: '5',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_6',
    AD_Config_group: 'panel',
    AD_Config_element_id: '6',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_7',
    AD_Config_group: 'panel',
    AD_Config_element_id: '7',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_8',
    AD_Config_group: 'panel',
    AD_Config_element_id: '8',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_in_article_1',
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '1',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_in_article_2',
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '2',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_in_article_3',
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '3',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '620px'
  },
  {
    id: 'panel_in_article_4',
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '4',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_in_article_5',
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '5',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_in_article_6',
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '6',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_in_article_7',
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '7',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_in_article_8',
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '8',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_in_article_9',
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '9',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'panel_in_article_10',
    AD_Config_group: 'panel_in_article',
    AD_Config_element_id: '10',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '300px'
  },
  {
    id: 'native_1',
    AD_Config_group: 'native',
    AD_Config_element_id: '1',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '100%'
  },
  {
    id: 'native_2',
    AD_Config_group: 'native',
    AD_Config_element_id: '2',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '100%'
  },
  {
    id: 'native_3',
    AD_Config_group: 'native',
    AD_Config_element_id: '3',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '100%'
  },
  {
    id: 'native_4',
    AD_Config_group: 'native',
    AD_Config_element_id: '4',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '100%'
  },
  {
    id: 'native_5',
    AD_Config_group: 'native',
    AD_Config_element_id: '5',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '100%'
  },
  {
    id: 'native_6',
    AD_Config_group: 'native',
    AD_Config_element_id: '6',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '100%'
  },
  {
    id: 'native_7',
    AD_Config_group: 'native',
    AD_Config_element_id: '7',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '100%'
  },
  {
    id: 'native_8',
    AD_Config_group: 'native',
    AD_Config_element_id: '8',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '100%'
  },
  {
    id: 'native_9',
    AD_Config_group: 'native',
    AD_Config_element_id: '9',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '100%'
  },
  {
    id: 'native_10',
    AD_Config_group: 'native',
    AD_Config_element_id: '10',
    deviceType: [SMARTPHONE],
    width: '100%',
    height: '100%'
  },
  {
    id: 'halfpage_1',
    enabled: false,
    AD_Config_group: 'halfpage',
    AD_Config_element_id: '1',
    deviceType: [SMARTPHONE],
    width: null,
    height: null
  },
  {
    id: 'halfpage_2',
    enabled: false,
    AD_Config_group: 'halfpage',
    AD_Config_element_id: '2',
    deviceType: [SMARTPHONE],
    width: null,
    height: null
  },
  {
    id: 'halfpage_3',
    enabled: false,
    AD_Config_group: 'halfpage',
    AD_Config_element_id: '3',
    deviceType: [SMARTPHONE],
    width: null,
    height: null
  },
  {
    id: 'halfpage_4',
    enabled: false,
    AD_Config_group: 'halfpage',
    AD_Config_element_id: '4',
    deviceType: [SMARTPHONE],
    width: null,
    height: null
  },
  {
    id: 'halfpage_5',
    enabled: false,
    AD_Config_group: 'halfpage',
    AD_Config_element_id: '5',
    deviceType: [SMARTPHONE],
    width: null,
    height: null
  },
  {
    id: 'halfpage_6',
    enabled: false,
    AD_Config_group: 'halfpage',
    AD_Config_element_id: '6',
    deviceType: [SMARTPHONE],
    width: null,
    height: null
  },
  {
    id: 'layer',
    AD_Config_group: 'layer',
    AD_Config_element_id: '1',
    deviceType: [SMARTPHONE],
    width: '1px',
    height: '1px'
  },
  {
    id: 'commercial_break',
    enabled: false,
    AD_Config_group: 'commercial_break',
    AD_Config_element_id: '1',
    deviceType: [SMARTPHONE],
    width: '1px',
    height: '1px'
  }
];

// Necessary duplication for rules with priorities
const warszawa_k2_PlaceholdersDesktop: PlaceholderForTests[] =
  warszawa_k2_PlaceholdersSmartphone.map(p => ({
    ...p,
    deviceType: [DESKTOP]
  }));

const warszawa_k2_PlaceholdersTablet: PlaceholderForTests[] =
  warszawa_k2_PlaceholdersSmartphone.map(p => ({
    ...p,
    deviceType: [TABLET]
  }));

const warszawa_k2_AdConfigSmartphone: AdConfigFactoryParams = {
  releaseServices: ['warszawak2'],
  releaseVersion: 'release/1.64.4',
  config_name: 'story',
  pageType: ['story_story_video'],
  serviceId: ['warszawa_k2'],
  placeholders: [
    ...warszawa_k2_PlaceholdersSmartphone,
    ...warszawa_k2_PlaceholdersDesktop,
    ...warszawa_k2_PlaceholdersTablet
  ]
};

const warszawa_k2AdConfigs: AdConfigFactoryParams[] = [warszawa_k2_AdConfigSmartphone];

export const adConfigDB: AdConfig[] = [
  ...ddtvnAdConfigs,
  ...warszawa_k2AdConfigs,
  {
    releaseServices: ['19'],
    releaseVersion: 'release/1.64.0',
    config_name: 'section_page_prod_1289',
    pageType: ['story_story_video'],
    serviceId: ['19'],
    pageId: [],
    section: [{ id: '1289', name: 'unboxing' }],
    placeholders: [
      {
        id: 'panel_pod_1_artykulem',
        enabled: false,
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '1',
        deviceType: [DESKTOP],
        width: '750px',
        height: '370px'
      },
      {
        id: 'panel_pod_1_artykulem',
        enabled: false,
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '1',
        deviceType: [TABLET],
        width: '750px',
        height: '370px'
      },
      {
        id: 'panel_pod_1_artykulem',
        enabled: false,
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '1',
        deviceType: [SMARTPHONE],
        width: '300px',
        height: '270px'
      },
      {
        id: 'panel_pod_3_artykulem',
        enabled: false,
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '2',
        deviceType: [DESKTOP],
        width: '750px',
        height: '370px'
      },
      {
        id: 'panel_pod_3_artykulem',
        enabled: false,
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '2',
        deviceType: [TABLET],
        width: '750px',
        height: '370px'
      },
      {
        id: 'panel_pod_3_artykulem',
        enabled: false,
        AD_Config_group: 'panel_pod_artykulem',
        AD_Config_element_id: '2',
        deviceType: [SMARTPHONE],
        width: '300px',
        height: '270px'
      }
    ]
  },
  {
    releaseServices: ['vod'],
    releaseVersion: 'release/1.60.0',
    config_name: 'story_4598886',
    pageType: ['story', 'story_content_hub'],
    serviceId: ['vod'],
    pageId: ['4598886'],
    section: [{ id: '1', name: 'wybrane_dla_ciebie' }],
    placeholders: [
      {
        id: 'superpanel',
        AD_Config_group: '',
        AD_Config_element_id: '',
        deviceType: [DESKTOP],
        width: '980px',
        height: '320px'
      },
      {
        id: 'superpanel',
        AD_Config_group: '',
        AD_Config_element_id: '',
        deviceType: [TABLET],
        width: '750px',
        height: '320px'
      },
      {
        id: 'superpanel',
        enabled: false,
        AD_Config_group: '',
        AD_Config_element_id: '',
        deviceType: [SMARTPHONE],
        width: null,
        height: null
      },
      {
        id: 'pasek_pod_nawigacja',
        enabled: false,
        AD_Config_group: '',
        AD_Config_element_id: '',
        deviceType: [DESKTOP],
        width: null,
        height: null
      },
      {
        id: 'pasek_pod_nawigacja',
        enabled: false,
        AD_Config_group: '',
        AD_Config_element_id: '',
        deviceType: [TABLET],
        width: null,
        height: null
      },
      {
        id: 'pasek_pod_nawigacja',
        AD_Config_group: '',
        AD_Config_element_id: '',
        deviceType: [SMARTPHONE],
        width: '100%',
        height: '140px'
      }
    ]
  },
  {
    releaseServices: ['tvn'],
    releaseVersion: 'release/1.62.0',
    config_name: 'story_4598886',
    pageType: ['story'],
    serviceId: ['tvn'],
    pageId: ['349985'],
    section: [{ id: '223', name: 'Najnowsze' }],
    placeholders: [
      {
        id: 'superpanel',
        AD_Config_group: '',
        AD_Config_element_id: '',
        deviceType: [DESKTOP],
        width: '980px',
        height: '320px'
      }
    ]
  }
].map(adConfigFactory);

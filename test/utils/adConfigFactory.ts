import { AdConfig, AdConfigAuditData, AdsLayoutsAdditionalData } from 'ads-layouts-tools';
import { cloneDeep } from 'lodash';
import { placeholderFactory, PlaceholderForTests } from 'TestUtils';
import { PartialDeep } from 'type-fest';

export type AdConfigFactoryParams = Pick<AdConfig, 'config_name' | 'pageType' | 'serviceId'> &
  Partial<Pick<AdConfig, 'pageId' | 'section'>> & {
    releaseServices: AdsLayoutsAdditionalData['releaseServices'];
    releaseVersion: AdConfigAuditData['releaseVersion'];
    placeholders: PlaceholderForTests[];
  };

export const adConfigFactory = ({
  releaseServices,
  releaseVersion,
  config_name,
  pageType,
  serviceId,
  pageId = [],
  section = [],
  placeholders
}: AdConfigFactoryParams): AdConfig => ({
  adsLayoutsAdditionalData: {
    releaseServices,
    releaseUrl: new URL('https://example.com/'),
    releaseName: `${releaseVersion}/`
  },
  auditData: {
    releaseVersion,
    modifiedDate: '',
    generatedBy: ''
  },
  config_name,
  src: '',
  pageType,
  serviceId,
  pageId,
  section,
  config: {
    masterId: '',
    bgPlugSrc: '',
    activationThresholds: { offset: null, percent: null, delay: 0 },
    trafficCategory: [],
    placeholders: placeholders.map(placeholderFactory)
  }
});

type AdConfigCloneParams = PartialDeep<
  Omit<AdConfigFactoryParams, 'placeholders'> & {
    placeholder: PlaceholderForTests;
  },
  { recurseIntoArrays: false }
>;

const modifyAdConfigClone = (
  sourceConfig: AdConfigFactoryParams,
  modifications: AdConfigCloneParams
) => {
  const { placeholder, ...rest } = modifications;

  const updatedConfig = { ...sourceConfig, ...rest };

  if (placeholder) {
    updatedConfig.placeholders = updatedConfig.placeholders.map(oldPlaceholder => ({
      ...oldPlaceholder,
      ...placeholder
    }));
  }

  return updatedConfig;
};

/**
 * Creates a clone of an existing ad config with modifications.
 * @param configs - Array of existing ad configs.
 * @param sourceConfigName - Name of the source config to clone.
 * @param targetConfigName - Name for the new cloned config.
 * @param params - Modifications to apply to the cloned config.
 * @returns The cloned and modified ad config.
 */
export const adConfigCloneFactory = (
  configs: AdConfigFactoryParams[],
  sourceConfigName: string,
  targetConfigName: string,
  params: AdConfigCloneParams
) => {
  const clonedConfig = cloneDeep(
    configs.find(({ config_name }) => config_name === sourceConfigName)!
  );

  if (!clonedConfig) {
    throw new Error(`Source config with name "${sourceConfigName}" not found`);
  }

  clonedConfig.config_name = targetConfigName;

  return modifyAdConfigClone(clonedConfig, params);
};

# This workflow is triggered on pull requests to the repository.
# It will run the unit tests with the specified Node.js version.

name: Unit tests

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.ref_name }}
  cancel-in-progress: true

env:
  NODE_VERSION: '22.15.0'
  NPM_CACHE: 'npm'

on:
  pull_request:
  workflow_dispatch:

jobs:
  unit-test:
    runs-on:
      group: k8s-runner-group-cwp

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: ${{ env.NPM_CACHE }}

      - name: Cache node_modules
        id: cache-node-modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key:
            ${{ runner.os }}-node-${{ env.NODE_VERSION }}-modules-${{ hashFiles('package-lock.json') }}
          restore-keys:
            ${{ runner.os }}-node-${{ env.NODE_VERSION }}-modules-${{ hashFiles('package-lock.json') }}

      - if: ${{ steps.cache-node-modules.outputs.cache-hit != 'true' }}
        name: Install dependencies
        run: npm ci

      - name: Unit test
        run: npm run test:gh

{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "noImplicitAny": true, "strict": true, "useUnknownInCatchVariables": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "esModuleInterop": false, "paths": {"InterfacesAndTypes": ["src/interfacesAndTypes"], "Utils/*": ["src/utils/*"], "Utils": ["src/utils"], "LocalCache/*": ["src/localCache/*"], "Helpers": ["src/helpers"], "Mocks": ["test/mocks"], "TestUtils": ["test/utils"], "test/*": ["test/*"], "src/*": ["src/*"]}}, "include": ["test/**/*", "src/events/getPlaceholderHelpers/*", "src/adConfigs/adConfig.helper.ts", "src/generator/generator.helper.ts"], "files": ["src/main.ts"]}
{"activationThresholds": {"delay": 0, "offset": null, "percent": null}, "adsConfigIdentificationData": {"appVersion": "1.52.1", "configName": "main_page", "modifiedDate": "2025-10-08 16:38:28", "releaseVersion": "release/1.74.0/", "rulesPackage": "hub_tvn_k2", "serviceId": ["tvn_wizytowki"]}, "bgPlugSrc": null, "debugData": {"allAvailableAdConfigGroups": ["group: top_premium, id: 1", "group: panel, id: 1", "group: panel, id: 2", "group: panel, id: 3", "group: panel, id: 4", "group: panel, id: 5", "group: panel, id: 6", "group: panel, id: 7", "group: panel, id: 8", "group: panel, id: 9", "group: panel, id: 10", "group: panel, id: 11", "group: panel, id: 12", "group: panel, id: 13", "group: panel, id: 14", "group: panel, id: 15", "group: native, id: 1", "group: native, id: 2", "group: native, id: 3", "group: native, id: 4", "group: native, id: 5", "group: native, id: 6", "group: native, id: 7", "group: native, id: 8", "group: native, id: 9", "group: native, id: 10", "group: native, id: 11", "group: native, id: 12", "group: halfpage, id: 1", "group: halfpage, id: 2", "group: halfpage, id: 3", "group: halfpage, id: 4", "group: halfpage, id: 5", "group: halfpage, id: 6", "group: halfpage, id: 7", "group: halfpage, id: 8", "group: halfpage, id: 9", "group: halfpage, id: 10", "group: halfpage, id: 11", "group: halfpage, id: 12", "group: layer, id: 1", "group: commercial_break, id: 1"], "allRulesCount": 9, "fullConfigName": "main_page", "releaseVersion": "release/1.74.0/", "reqBodyType": "section", "rulesStats": {"fail": [{"conditionPass": true, "eventPass": false, "ruleName": "halfpageInformationModulesTvn24RuleK2", "selectedPlaceholders": []}, {"conditionPass": true, "eventPass": false, "ruleName": "nativeInformationModulesTvnRule24K2", "selectedPlaceholders": []}, {"conditionPass": true, "eventPass": false, "ruleName": "halfpageGlobalNewsTvn24RuleK2", "selectedPlaceholders": []}, {"conditionPass": true, "eventPass": false, "ruleName": "panelGlobalNewsRuleTvn24K2", "selectedPlaceholders": []}, {"conditionPass": true, "eventPass": false, "ruleName": "afterModulesTvn24RuleK2", "selectedPlaceholders": []}, {"conditionPass": true, "eventPass": false, "ruleName": "nativeGlobalNewsTvn24RuleK2", "selectedPlaceholders": []}], "success": [{"conditionPass": true, "eventPass": true, "mergePass": true, "mergeResult": [{"AD_Config_element_id": "1", "AD_Config_group": "commercial_break", "id": "93", "type": "placeholder"}], "ruleName": "commercialBreakRuleGeneralK2", "selectedPlaceholders": [{"id": "93", "type": "placeholder"}]}, {"conditionPass": true, "eventPass": true, "mergePass": true, "mergeResult": [{"AD_Config_element_id": "1", "AD_Config_group": "top_premium", "id": "2", "type": "placeholder"}], "ruleName": "topPremiumRuleGeneralK2", "selectedPlaceholders": [{"id": "2", "type": "placeholder"}]}, {"conditionPass": true, "eventPass": true, "mergePass": true, "mergeResult": [{"AD_Config_element_id": "1", "AD_Config_group": "layer", "id": "95", "type": "placeholder"}], "ruleName": "layerRuleGeneralK2", "selectedPlaceholders": [{"id": "95", "type": "placeholder"}]}]}, "shortSuccessMergeStats": [{"group": "commercial_break", "groupId": "1", "placeholderId": "93"}, {"group": "top_premium", "groupId": "1", "placeholderId": "2"}, {"group": "layer", "groupId": "1", "placeholderId": "95"}], "successConditionsCount": 9, "successEventsCount": 3, "successMergeCount": 3}, "masterId": "", "placeholders": [{"AD_Config_element_id": "1", "AD_Config_group": "commercial_break", "adServer": "gam", "adSlots": [{"adServer": "adocean", "height": null, "placementId": null, "slaveId": null, "type": ""}, {"adServer": "gam", "adUnitPath": "/65073904/11070110/K_TVN_WIZYTOWKI/main_page/commercial_break", "height": null, "kValues": {"placeholder": "commercial_break", "root": "gam", "slot": "commercial_break"}, "sizes": [[1, 1]]}], "bidders": [], "code": "", "configId": "commercial_break", "deviceType": ["desktop"], "enabled": true, "height": "0px", "id": "93", "mediaTypes": {"banner": {"sizes": [[1, 1]]}}, "type": "placeholder", "width": "0px"}, {"AD_Config_element_id": "1", "AD_Config_group": "top_premium", "adServer": "gam", "adSlots": [{"adServer": "adocean", "height": null, "placementId": null, "slaveId": null, "type": ""}, {"adServer": "gam", "adUnitPath": "/65073904/11070110/K_TVN_WIZYTOWKI/main_page/top_premium", "height": null, "kValues": {"placeholder": "top_premium", "root": "gam", "slot": "top_premium"}, "sizes": [[1920, 150], [728, 90], [750, 100]]}], "bidders": [], "code": "", "configId": "top_premium", "deviceType": ["desktop"], "enabled": true, "height": "150px", "id": "2", "mediaTypes": {"banner": {"sizes": [[1920, 150], [728, 90], [750, 100]]}}, "type": "placeholder", "width": "100%"}, {"AD_Config_element_id": "1", "AD_Config_group": "layer", "adServer": "gam", "adSlots": [{"adServer": "adocean", "height": null, "placementId": null, "slaveId": null, "type": ""}, {"adServer": "gam", "adUnitPath": "/65073904/11070110/K_TVN_WIZYTOWKI/main_page/layer", "height": null, "kValues": {"placeholder": "layer", "root": "gam", "slot": "layer"}, "sizes": [[1, 1]]}], "bidders": [], "code": "", "configId": "layer", "deviceType": ["desktop"], "enabled": true, "height": "0px", "id": "95", "mediaTypes": {"banner": {"sizes": [[1, 1]]}}, "type": "placeholder", "width": "0px"}], "requestMeta": {"accessModel": "avod", "deviceType": "desktop", "locationInfoPageId": null, "locationInfoPageType": "main_page", "locationInfoSectionId": "733", "locationInfoSectionName": "Ku<PERSON>", "paywall": "no", "serviceEnv": "stage2", "serviceId": "tvn_wizytowki", "serviceName": "kuba_wojewodzki", "siteVersion": "k2", "siteVersionIdentifier": "733_20241014223811_83dafd426681640b88dec4718e34c390", "time": "1759993200000"}, "trafficCategory": ["kobieta", "manager", "mezczyzna"], "version": "release/1.74.0"}
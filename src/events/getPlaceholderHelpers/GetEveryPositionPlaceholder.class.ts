import {
  AdConfigDeviceTypeEnum,
  IEvent,
  LogLevel,
  PlaceholderPositionEnum,
  WeightsConfig
} from 'ads-layouts-tools';

import { FactType, PlaceholderType } from 'InterfacesAndTypes';
import { log } from 'Utils';
import { isPlaceholder, getPlaceholdersFromFact } from 'Helpers';

const above = PlaceholderPositionEnum.ABOVE;
const under = PlaceholderPositionEnum.UNDER;

export class GetEveryPositionPlaceholderClass {
  constructor(private getCurrentVariantWeights: () => WeightsConfig | null) {}

  public get(
    event: IEvent,
    fact: FactType[],
    deviceType: AdConfigDeviceTypeEnum
  ): PlaceholderType[] {
    const config = event.params.placeholder;

    if (config.placeholderPositions?.length) {
      return this.getPlaceholdersByPositions(config, fact);
    }

    if (config.elementsPositions?.length) {
      return this.getPlaceholdersByElements(config, fact, deviceType);
    }

    log('INVALID_EVENT', { event }, LogLevel.dev);
    return [];
  }

  private getPlaceholdersByPositions(
    config: IEvent['params']['placeholder'],
    fact: FactType[]
  ): PlaceholderType[] {
    let placeholders = getPlaceholdersFromFact(fact);

    if (config.placeholderType) {
      placeholders = placeholders.filter(p => p.type === config.placeholderType);
    }

    if (config.countBackwards) {
      placeholders = placeholders.reverse();
    }

    const filtered: PlaceholderType[] = [];

    if (config.placeholderPositions?.includes('last') && !config.countBackwards) {
      filtered.push(placeholders[placeholders.length - 1]);
    }

    filtered.push(
      ...placeholders.filter((_, i) => config.placeholderPositions?.includes(i + 1))
    );

    return filtered;
  }

  private getPlaceholdersByElements(
    config: IEvent['params']['placeholder'],
    fact: FactType[],
    deviceType: AdConfigDeviceTypeEnum
  ): PlaceholderType[] {
    let index = 0;

    const orderedFacts = config.countBackwards ? [...fact].reverse() : fact;

    const factsWithIndex = orderedFacts.map(el => {
      if (isPlaceholder(el.type)) {
        return { ...el, nonPlaceholderIndex: undefined };
      }

      index += config.enableVariant
        ? this.getVariantWeight(deviceType, el.type, el.meta?.variant)
        : 1;

      return { ...el, nonPlaceholderIndex: index };
    });

    const selected: FactType[] = [];

    for (const [position, location] of config.elementsPositions || []) {
      const idx = factsWithIndex.findIndex(el => el.nonPlaceholderIndex === position);

      if (idx === -1) continue;

      const placeholderIdx = location === PlaceholderPositionEnum.UNDER ? idx + 1 : idx - 1;
      const candidate = factsWithIndex[placeholderIdx];

      if (candidate && isPlaceholder(candidate.type)) {
        selected.push(candidate);
      }
    }

    return selected.map(({ nonPlaceholderIndex, meta, ...rest }) => rest);
  }

  /**
   * Returns weight of given variant for given module type and device type.
   * If variant is unknown or fact doesn't have variant, returns 1.
   *
   * @param deviceType - desktop, tablet or smartphone
   * @param type - module type
   * @param variant - variant
   * @returns weight of given variant
   */
  getVariantWeight(
    deviceType: AdConfigDeviceTypeEnum,
    type: string,
    variant: string | undefined
  ): number {
    if (!variant) {
      return 1;
    }

    const variantWeights = this.getCurrentVariantWeights();
    const moduleWithVariantOrNumber = variantWeights?.[deviceType]?.[type];

    if (typeof moduleWithVariantOrNumber === 'number') {
      return moduleWithVariantOrNumber;
    }

    const weight = moduleWithVariantOrNumber?.[variant];

    if (weight === undefined) {
      log('UNKNOWN_VARIANT', { deviceType, type, variant }, LogLevel.dev);

      return 1;
    }

    return weight;
  }
}

import { IEvent, LogLevel, PlaceholderPositionEnum, IExcludeFacts } from 'ads-layouts-tools';
import {
  FactType,
  FactWithNeighbors,
  PlaceholderType,
  PlaceholderTypeUsable
} from 'InterfacesAndTypes';

import { log } from 'Utils';
import { isPlaceholder } from 'Helpers';

const above = PlaceholderPositionEnum.ABOVE;
const under = PlaceholderPositionEnum.UNDER;

export class GetEveryXPlaceholderClass {
  public get(event: IEvent, fact: FactType[]): PlaceholderType[] {
    const { every, max, ommitLast, position, element, startIndex, exclude } =
      event.params.placeholder;

    if (!every || fact.length === 0) {
      log('INVALID_EVERY_X_EVENT', { event }, LogLevel.warn);
      return [];
    }

    const cleanedFacts = this.omitLastPlaceholderIfNeeded(fact, ommitLast);
    const extendedFacts = this.extendFactsWithNeighbors(cleanedFacts);

    this.markElementFilter(extendedFacts, element);
    this.applyExclusions(extendedFacts, exclude);
    this.applyStartIndex(extendedFacts, startIndex);

    const activeFacts = extendedFacts.filter(f => f.canBeUsed);
    let placeholders = this.selectPlaceholders(activeFacts, every, position, exclude);

    if (max) {
      placeholders = placeholders.slice(0, +max);
    }

    return placeholders.map(({ id, type }) => ({ id, type }));
  }

  private omitLastPlaceholderIfNeeded(facts: FactType[], ommitLast?: boolean): FactType[] {
    if (ommitLast && isPlaceholder(facts[facts.length - 1]?.type)) {
      return facts.slice(0, -1);
    }
    return facts;
  }

  private extendFactsWithNeighbors(facts: FactType[]): FactWithNeighbors[] {
    const factsWithState = facts.map(f => ({ ...f, canBeUsed: true }));
    const result: FactWithNeighbors[] = [];
    let index = 0;

    for (let i = 0; i < factsWithState.length; i++) {
      const el = factsWithState[i];
      if (isPlaceholder(el.type)) continue;

      result.push({
        ...el,
        canBeUsed: true,
        [above]:
          i > 0 && isPlaceholder(factsWithState[i - 1].type) ? factsWithState[i - 1] : null,
        [under]:
          i < factsWithState.length - 1 && isPlaceholder(factsWithState[i + 1].type)
            ? factsWithState[i + 1]
            : null,
        nonPlaceholderIndex: ++index
      });
    }

    return result;
  }

  private markElementFilter(facts: FactWithNeighbors[], element?: string[]) {
    if (!element?.length) return;

    facts.forEach(f => {
      f.canBeUsed = element.includes(f.type);
    });
  }

  private applyExclusions(facts: FactWithNeighbors[], exclude?: IExcludeFacts) {
    if (!exclude) return;

    const { omittedFactNames, skipOverFactNames } = exclude;

    facts.forEach(f => {
      if (omittedFactNames.includes(f.type)) {
        f[above]?.canBeUsed && (f[above].canBeUsed = false);
        f[under]?.canBeUsed && (f[under].canBeUsed = false);
      }
      if (skipOverFactNames?.includes(f.type)) {
        f.canBeUsed = false;
      }
    });
  }

  private applyStartIndex(facts: FactWithNeighbors[], startIndex?: number) {
    if (!startIndex) return;

    for (const f of facts) {
      if (f.nonPlaceholderIndex < startIndex) {
        f.canBeUsed = false;
      } else {
        break;
      }
    }
  }

  private selectPlaceholders(
    facts: FactWithNeighbors[],
    every: number,
    position?: PlaceholderPositionEnum.ABOVE | PlaceholderPositionEnum.UNDER,
    exclude?: any
  ): PlaceholderTypeUsable[] {
    const result: PlaceholderTypeUsable[] = [];

    if (exclude && position) {
      let counter = 0;
      const positionIsAbove = position === above;

      for (const f of facts) {
        if (++counter < +every) continue;

        const target = positionIsAbove && f[above]?.canBeUsed ? f[above] : f[under];

        if (target?.canBeUsed) {
          result.push(target);
          target.canBeUsed = false;
          counter = 0;
        }
      }
    } else {
      const pos = position ?? above;
      return facts
        .filter((_, i) => (i + 1) % +every === 0)
        .map(f => f[pos])
        .filter((p): p is PlaceholderTypeUsable => !!p);
    }

    return result;
  }
}

import { IEvent, PlaceholderPositionEnum } from 'ads-layouts-tools';
import { FactType, PlaceholderType } from 'InterfacesAndTypes';
import { isPlaceholder, returnAsArrayEmpty } from 'Helpers';

export class GetNearToElementPlaceholderClass {
  /**
   * Finds the nearest placeholder element based on the given event and facts.
   *
   * @param event The event object containing the placeholder information
   * @param fact The array of facts to search for the matching element
   * @returns An array of PlaceholderType representing the nearest placeholder element
   */
  public get(event: IEvent, fact: FactType[]): PlaceholderType[] {
    const { position, element } = event.params.placeholder;

    let matchingElementIndex: number | null = null;

    const elementArray: string[] = returnAsArrayEmpty(element);

    for (const el of elementArray) {
      const index = fact.findIndex(fel => fel?.type === el);
      if (index !== -1) {
        matchingElementIndex = index;
        break;
      }
    }
    if (matchingElementIndex === null) {
      return [];
    }

    const updatedIndex =
      position === PlaceholderPositionEnum.UNDER
        ? matchingElementIndex + 1
        : matchingElementIndex - 1;

    if (
      updatedIndex < 0 ||
      updatedIndex >= fact.length ||
      !isPlaceholder(fact[updatedIndex].type)
    ) {
      return [];
    }

    return [fact[updatedIndex]];
  }
}

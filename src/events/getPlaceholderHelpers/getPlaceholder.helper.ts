import {
  AdConfigDeviceTypeEnum,
  EventTypeEnum,
  IEvent,
  LogLevel,
  PlaceholderPositionEnum,
  WeightsConfig
} from 'ads-layouts-tools';

import { FactType, PlaceholderType } from 'InterfacesAndTypes';
import { log } from 'Utils';
import { GetEveryXPlaceholderClass } from './GetEveryXPlaceholder.class';
import { GetEveryPositionPlaceholderClass } from './GetEveryPositionPlaceholder.class';
import { GetNearToElementPlaceholderClass } from './GetNearToElementPlaceholder.class';
import { GetPlaceholdersNearIndexesClass } from './GetPlaceholdersNearIndexes.class';

const above = PlaceholderPositionEnum.ABOVE;
const under = PlaceholderPositionEnum.UNDER;

export class PlaceholderHelper {
  private everyXPlaceholder;
  private everyPositionPlaceholder;
  private nearToElementPlaceholder;
  private placeholdersNearIndexes;

  constructor(private getCurrentVariantWeights: () => WeightsConfig | null) {
    this.everyXPlaceholder = new GetEveryXPlaceholderClass();
    this.everyPositionPlaceholder = new GetEveryPositionPlaceholderClass(
      getCurrentVariantWeights
    );
    this.nearToElementPlaceholder = new GetNearToElementPlaceholderClass();
    this.placeholdersNearIndexes = new GetPlaceholdersNearIndexesClass();
  }

  getPlaceholdersByType(
    event: IEvent,
    elements: FactType[],
    deviceType: AdConfigDeviceTypeEnum
  ): PlaceholderType[] {
    switch (event.type) {
      case EventTypeEnum.EVERY_POSITION:
        return this.everyPositionPlaceholder.get(event, elements, deviceType);

      case EventTypeEnum.EVERY_X:
        return this.everyXPlaceholder.get(event, elements);

      case EventTypeEnum.NEAR_TO:
        return this.nearToElementPlaceholder.get(event, elements);

      case EventTypeEnum.NEAR_TO_INDEXES:
        return this.placeholdersNearIndexes.get(event, elements);

      default:
        return [];
    }
  }

  getPlaceholdersByEventTypeForNotProvidedContainingFact(
    event: IEvent,
    containingFact: FactType[]
  ): PlaceholderType[] {
    switch (event.type) {
      case EventTypeEnum.NEAR_TO:
        return this.nearToElementPlaceholder.get(event, containingFact);

      case EventTypeEnum.NEAR_TO_INDEXES:
        return this.placeholdersNearIndexes.get(event, containingFact);

      default:
        log('INVALID_FUNCTION_INVOCATION_FOR_GIVEN_EVENT_TYPE', { event }, LogLevel.warn);
        return [];
    }
  }
}

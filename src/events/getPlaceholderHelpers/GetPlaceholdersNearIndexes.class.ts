import { IEvent, LogLevel, PlaceholderPositionEnum } from 'ads-layouts-tools';
import { FactType, PlaceholderType, PosFactType } from 'InterfacesAndTypes';
import { log } from 'Utils';
import { isPlaceholder, returnAsArrayEmpty } from 'Helpers';

export class GetPlaceholdersNearIndexesClass {
  public get(event: IEvent, fact: FactType[]): PlaceholderType[] {
    const { position, element, elementsIndexes, countBackwards } = event.params.placeholder;

    if (!elementsIndexes?.length) {
      log('INVALID_EVENT_NEEDS_ELEMENTS_INDEXES', {}, LogLevel.warn);
      return [];
    }

    const facts = countBackwards ? [...fact].reverse() : fact;

    const matchingElement = returnAsArrayEmpty(element).find(elementItem =>
      facts.some(({ type }) => type === elementItem)
    );

    const factWithIndex = facts.reduce((acc: PosFactType[], curr, index) => {
      if (curr?.type === matchingElement) {
        acc.push({
          ...curr,
          nonPlaceholderIndex: index
        });
      }
      return acc;
    }, []);

    const expectedPlaceholders: PlaceholderType[] = [];

    factWithIndex.forEach(({ nonPlaceholderIndex }, factIndex) => {
      if (elementsIndexes.includes(factIndex + 1)) {
        const updatedIndex =
          position === PlaceholderPositionEnum.UNDER
            ? nonPlaceholderIndex + 1
            : nonPlaceholderIndex - 1;

        if (
          updatedIndex >= 0 &&
          updatedIndex < facts.length &&
          isPlaceholder(facts[updatedIndex].type)
        ) {
          expectedPlaceholders.push(facts[updatedIndex]);
        }
      }
    });

    return expectedPlaceholders;
  }
}

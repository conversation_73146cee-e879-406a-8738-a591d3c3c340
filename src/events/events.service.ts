import { Injectable, OnModuleInit } from '@nestjs/common';
import { Interval } from '@nestjs/schedule';
import { isPlaceholder, returnAsArrayEmpty } from 'Helpers';
import { FactType, FactTypeWithElements, IAlmanac, PlaceholderType } from 'InterfacesAndTypes';
import { log } from 'Utils';
import {
  AdConfigDeviceTypeEnum,
  EventTypeEnum,
  IEvent,
  LogLevel,
  WeightsConfig
} from 'ads-layouts-tools';
import { ENV } from '../envalidConfig';
import { VariantWeightsConfigService } from '../variantWeightsConfig/variantWeightsConfig.service';
import { PlaceholderHelper } from './getPlaceholderHelpers/getPlaceholder.helper';

@Injectable()
export class EventsService implements OnModuleInit {
  private variantWeights: WeightsConfig | null = null;
  private readonly placeholderHelper = new PlaceholderHelper(this.getCurrentVariantWeights);

  constructor(private readonly variantService: VariantWeightsConfigService) {}

  async onModuleInit() {
    log('EVENTS_SERVICE_INIT', {}, LogLevel.dev);
    await this.updateVariantWeights();
  }

  @Interval(ENV.LOCAL_CACHE_VARIANT_WEIGHTS_TTL)
  async updateVariantWeights(omitCache = false) {
    this.variantWeights = await this.variantService.get(omitCache);
  }

  async getSiteMapPlaceholdersBasedOnEvents(
    successfulEvent: IEvent,
    resultAlmanac: IAlmanac,
    allFactsNames: string[],
    deviceType: AdConfigDeviceTypeEnum
  ): Promise<PlaceholderType[]> {
    const hasContainingFact = !!successfulEvent?.params?.fact?.length;

    let siteMapMatchedPlaceholders: PlaceholderType[] = [];

    if (hasContainingFact) {
      // containing fact for event set explicitly
      siteMapMatchedPlaceholders = await this.getPlaceholdersByEventType(
        successfulEvent,
        resultAlmanac,
        allFactsNames,
        deviceType
      );
    } else {
      // containing fact for event not provided
      const almanacFactsNames: string[] = resultAlmanac.factMap.keys();

      const placeholdersElements = returnAsArrayEmpty(
        successfulEvent.params.placeholder.element
      );

      const containingFact = await this.getContainingFact(
        almanacFactsNames,
        placeholdersElements,
        resultAlmanac
      );

      if (!containingFact) {
        return [];
      }

      siteMapMatchedPlaceholders =
        this.placeholderHelper.getPlaceholdersByEventTypeForNotProvidedContainingFact(
          successfulEvent,
          containingFact
        );
    }

    return siteMapMatchedPlaceholders;
  }

  private async getContainingFact(
    almanacFactsNames: string[],
    placeholdersElements: string[],
    resultAlmanac: IAlmanac
  ): Promise<FactType[] | undefined> {
    for (const fact of almanacFactsNames) {
      const factContent: FactType[] = await resultAlmanac.factValue(fact);

      if (factContent) {
        const factContentPossibleArray = returnAsArrayEmpty(factContent);

        for (const placeholderElement of placeholdersElements) {
          const checkFactContent = factContentPossibleArray.find(
            f => f.type === placeholderElement
          );
          if (checkFactContent) {
            return factContent;
          }
        }
      }
    }
  }

  async getPlaceholdersByEventType(
    event: IEvent,
    almanac: IAlmanac,
    allFactsNames: string[],
    deviceType: AdConfigDeviceTypeEnum
  ): Promise<PlaceholderType[]> {
    const {
      fact,
      factPosition,
      parentFact,
      searchInFacts,
      excludeFromSearch,
      containsSomeType
    } = event.params;

    const anyFactAccepted =
      event.type === EventTypeEnum.NEAR_TO_INDEXES && fact.includes('any');

    const factNames = anyFactAccepted ? allFactsNames : fact;

    if (containsSomeType) {
      return this.processMultipleFacts({
        factNames,
        containsSomeType,
        searchInFacts,
        excludeFromSearch,
        event,
        almanac,
        deviceType
      });
    }

    return this.processSingleFacts({
      factNames,
      allFactsNames,
      anyFactAccepted,
      factPosition,
      parentFact,
      event,
      almanac,
      deviceType
    });
  }

  private async processMultipleFacts(params: {
    factNames: string[];
    containsSomeType: string;
    searchInFacts: string[];
    excludeFromSearch?: string[];
    event: IEvent;
    almanac: IAlmanac;
    deviceType: AdConfigDeviceTypeEnum;
  }): Promise<PlaceholderType[]> {
    const {
      factNames,
      containsSomeType,
      searchInFacts,
      excludeFromSearch,
      event,
      almanac,
      deviceType
    } = params;

    const allPlaceholders: PlaceholderType[][] = [];

    for (const eventFact of returnAsArrayEmpty(factNames)) {
      const factValues: FactTypeWithElements[] | undefined =
        await almanac.factValue(eventFact);

      if (!factValues?.length) continue;

      const factsToSearch = this.filterFacts(factValues, searchInFacts, excludeFromSearch);

      const expectedFacts = factsToSearch.filter(el =>
        el.elements?.some(item => item.type === containsSomeType)
      );

      for (const fact of expectedFacts) {
        const placeholders = this.placeholderHelper.getPlaceholdersByType(
          event,
          fact.elements,
          deviceType
        );
        allPlaceholders.push(placeholders);
      }
    }

    return allPlaceholders.flat();
  }

  private async processSingleFacts(params: {
    factNames: string[];
    allFactsNames: string[];
    anyFactAccepted: boolean;
    factPosition?: number;
    parentFact?: string;
    event: IEvent;
    almanac: IAlmanac;
    deviceType: AdConfigDeviceTypeEnum;
  }): Promise<PlaceholderType[]> {
    const {
      factNames,
      allFactsNames,
      anyFactAccepted,
      factPosition,
      parentFact,
      event,
      almanac,
      deviceType
    } = params;

    for (const eventFact of factNames) {
      if (!allFactsNames.includes(eventFact)) continue;

      const elements = await this.getExpectedElements(
        eventFact,
        factPosition,
        parentFact,
        anyFactAccepted,
        almanac
      );

      if (!elements) continue;

      return this.placeholderHelper.getPlaceholdersByType(event, elements, deviceType);
    }

    log('EVENT_TYPE_DID_NOT_MATCH', { event }, LogLevel.dev);
    return [];
  }

  private filterFacts(
    facts: FactTypeWithElements[],
    includeTypes: string[],
    excludeTypes?: string[]
  ): FactTypeWithElements[] {
    let result =
      includeTypes[0] === 'all' ? facts : facts.filter(el => includeTypes.includes(el.type));

    if (excludeTypes?.length) {
      result = result.filter(el => !excludeTypes.includes(el.type));
    }

    return result;
  }

  private async getExpectedElements(
    eventFact: string,
    factPosition?: number,
    parentFact?: string,
    anyFactAccepted = false,
    almanac?: IAlmanac
  ): Promise<FactType[] | undefined | null> {
    if (!factPosition || !parentFact || !almanac) {
      return almanac?.factValue(eventFact);
    }

    let parentFacts: FactTypeWithElements[] | undefined = await almanac.factValue(parentFact);
    if (!parentFacts?.length) return null;

    parentFacts = parentFacts.filter(el =>
      anyFactAccepted ? !isPlaceholder(el.type) : el.type === eventFact
    );

    const factAtPosition = parentFacts[factPosition - 1];
    return factAtPosition?.elements;
  }

  public getCurrentVariantWeights(): WeightsConfig | null {
    return this.variantWeights;
  }
}

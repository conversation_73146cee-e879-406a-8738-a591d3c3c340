import {
  AdConfig,
  AdConfigDeviceTypeEnum,
  CustomHttpStatus,
  ExtEnableDataEnum,
  ExtScheduleDataEnum,
  LogLevel,
  PlaceholdersDetails
} from 'ads-layouts-tools';

import { ExtensionService } from '../extensionConfig/extension.service';
import { IFilteredAdConfigs } from 'InterfacesAndTypes';
import { CreateException, log } from 'Utils';
import { ENV } from '../envalidConfig';

export class AdConfigHelper {
  constructor(private extensionService: ExtensionService) {}

  public partialFilterAdConfigs(
    serviceId: string,
    metaTime: number,
    deviceType: AdConfigDeviceTypeEnum,
    release: string,
    pageType: string,
    sectionId?: string,
    pageId?: string | null,
    omitCache = false
  ) {
    return async (adConfig: AdConfig): Promise<IFilteredAdConfigs> => {
      if (ENV.LOG_FILTERED_PLACEHOLDERS === 'ENABLED') {
        log(
          'CONFIG_SELECTED_DETAILS',
          {
            configName: adConfig.config_name,
            releaseName: adConfig.adsLayoutsAdditionalData.releaseName,
            sectionId: adConfig.section,
            pageType: adConfig.pageType,
            pageId: adConfig.pageId,
            omitCache
          },
          LogLevel.dev
        );

        log(
          'ADCONFIGS_SELECTED_DETAILS',
          {
            totalAdConfigsAmount: adConfig.config.placeholders.length.toString(),
            baseAdConfigsInfo: adConfig.config.placeholders.map(p => ({
              id: p.id,
              deviceType: p.deviceType,
              enabled: p.enabled,
              group: p.AD_Config_group,
              groupId: p.AD_Config_element_id
            }))
          },
          LogLevel.dev
        );
      }

      if (adConfig.config.placeholders.length === 0) {
        log(
          `WARN_${CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_PARAMS}_CANNOT_FIND_ADCONFIGS_FOR_PARAMS`,
          { serviceId, deviceType, release, pageType, sectionId, pageId },
          LogLevel.warn
        );
        throw CreateException({
          message: `Unable to find any adConfigs for given params: serviceId: ${serviceId}, deviceType: ${deviceType}, release: ${release}, pageType: ${pageType}, sectionId: ${sectionId}, pageId: ${pageId}`,
          statusCode: CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_PARAMS
        });
      }

      return this.filterAdConfigsBody(
        adConfig,
        metaTime,
        serviceId,
        deviceType,
        sectionId,
        omitCache
      );
    };
  }

  public formatServiceId(serviceId: string): string {
    const searchServiceIdTemp = serviceId === 'czt' ? 'czt_tvn' : serviceId;
    return searchServiceIdTemp.replaceAll('_', '');
  }

  public selectBestMatchingConfig(
    configs: AdConfig[],
    sectionId: string | undefined,
    pageId: string | null | undefined,
    filter: (c: AdConfig) => Promise<IFilteredAdConfigs>,
    release: string,
    serviceId: string
  ): Promise<IFilteredAdConfigs> {
    const matchSectionAndPage = configs.find(
      c =>
        sectionId &&
        pageId &&
        c.section.some(s => s.id === sectionId) &&
        c.pageId.includes(pageId)
    );

    if (matchSectionAndPage) {
      this.logMatch(
        'CONFIG_RETURN_BY_DEFAULT_PARAMS_PLUS_SECTION_ID_AND_PAGE_ID',
        matchSectionAndPage,
        pageId
      );
      return filter(matchSectionAndPage);
    }

    const matchSectionOnly = configs.find(
      c => sectionId && c.section.some(s => s.id === sectionId)
    );
    if (matchSectionOnly) {
      this.logMatch('CONFIG_RETURN_BY_DEFAULT_PARAMS_PLUS_SECTION_ID', matchSectionOnly);
      return filter(matchSectionOnly);
    }

    const emptySectionConfigs = configs.filter(c => c.section.length === 0);

    log(
      'CONFIG_RETURN_BY_DEFAULT_PARAMS',
      {
        amountOfMatchedConfigs: configs.length.toString(),
        matchedConfigNames: configs.map(c => c.config_name),
        amountOnlyEmptySectionIdConfigs: emptySectionConfigs.length.toString(),
        onlyEmptySectionIdConfigsNames: emptySectionConfigs.map(c => c.config_name)
      },
      LogLevel.dev
    );

    if (emptySectionConfigs.length > 1) {
      log(
        'WARN_FOUND_MORE_THAN_ONE_CONFIG',
        {
          amountOfMatchedConfigs: emptySectionConfigs.length.toString(),
          matchedConfigNames: emptySectionConfigs.map(c => c.config_name),
          release,
          serviceId
        },
        LogLevel.warn
      );
    }

    return filter(emptySectionConfigs[0]);
  }

  public logAndThrowConfigNotFound(
    serviceId: string,
    release: string,
    pageType: string,
    sectionId?: string,
    pageId?: string | null
  ): never {
    const errMsg = `WARN_${CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_PARAMS}_CANNOT_FIND_ANY_CONFIG`;
    log(errMsg, { serviceId, pageType, release, sectionId, pageId }, LogLevel.warn);

    throw CreateException({
      message: `Cannot find any adConfigs for provided params release: ${release}, pageType: ${pageType}, sectionId: ${sectionId}, pageId: ${pageId}`,
      statusCode: CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_PARAMS
    });
  }

  private logMatch(event: string, config: AdConfig, pageId?: string | null) {
    log(
      event,
      {
        amountOfMatchedConfigs: '1',
        matchedConfigNames: config.config_name,
        ...(pageId ? { pageId } : {})
      },
      LogLevel.dev
    );
  }

  public async filterAdConfigsBody(
    adConfig: AdConfig,
    metaTime: number,
    serviceId: string,
    deviceType: AdConfigDeviceTypeEnum,
    sectionId?: string,
    omitCache = false
  ): Promise<IFilteredAdConfigs> {
    const {
      config_name: configName,
      serviceId: serviceIdFromConfig,
      auditData: { modifiedDate }
    } = adConfig;

    const { activationThresholds, bgPlugSrc, masterId, trafficCategory } = adConfig.config;

    const enabledPlaceholders = this.filterEnabledPlaceholders(adConfig, sectionId);

    const deviceTypeFilteredPlaceholders = this.filterByDeviceType(
      enabledPlaceholders,
      deviceType
    );

    const extensionFilteredPlaceholders = await this.filterBaseOnExtensionConfig(
      deviceTypeFilteredPlaceholders,
      serviceId,
      metaTime,
      deviceType,
      omitCache
    );

    return {
      placeholders: extensionFilteredPlaceholders,
      commonConfigFields: {
        masterId,
        bgPlugSrc,
        activationThresholds,
        trafficCategory
      },
      configName,
      serviceIdFromConfig,
      modifiedDate
    };
  }

  public filterEnabledPlaceholders(
    adConfig: AdConfig,
    sectionId?: string
  ): PlaceholdersDetails[] {
    const filteredByEnabled = adConfig.config.placeholders.filter(p => p.enabled);

    if (filteredByEnabled.length === 0) {
      const errMsg = `WARN_${CustomHttpStatus.CANNOT_FIND_ANY_ENABLED_ADCONFIGS}_CANNOT_FIND_ANY_ENABLED_ADCONFIGS`;
      log(errMsg, {}, LogLevel.warn);

      const disabledAdConfigsCount = adConfig.config.placeholders.length;
      const sectionIdStr = sectionId ? `, sectionId: ${sectionId}` : '';

      throw CreateException({
        message: `${disabledAdConfigsCount} adConfigs were found, but all of them are disabled. No enabled adConfigs matched the provided parameters - release: ${adConfig.adsLayoutsAdditionalData.releaseName}, pageType: ${adConfig.pageType}${sectionIdStr}, pageId: ${adConfig.pageId}.`,
        statusCode: CustomHttpStatus.CANNOT_FIND_ANY_ENABLED_ADCONFIGS
      });
    }
    return filteredByEnabled;
  }

  public filterByDeviceType(
    placeholders: PlaceholdersDetails[],
    deviceType: AdConfigDeviceTypeEnum
  ): PlaceholdersDetails[] {
    const deviceTypeFilteredPlaceholders = placeholders.filter(p =>
      p.deviceType.includes(deviceType)
    );

    if (ENV.LOG_FILTERED_PLACEHOLDERS === 'ENABLED') {
      log(
        'ADCONFIGS_DEVICE_TYPE_FILTERED_AMOUNT',
        { deviceTypefilteredPlaceholders: deviceTypeFilteredPlaceholders.length.toString() },
        LogLevel.dev
      );
    }

    if (!deviceTypeFilteredPlaceholders.length) {
      const errMsg = `WARN_${CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_DEVICE}_CANNOT_FIND_ANY_ADCONFIGS_FOR_GIVEN_DEVICETYPE`;
      log(errMsg, { deviceType }, LogLevel.warn);
      throw CreateException({
        message: `Cannot find any adConfigs for provided params: deviceType: ${deviceType}`,
        statusCode: CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_DEVICE
      });
    }
    return deviceTypeFilteredPlaceholders;
  }

  async filterBaseOnExtensionConfig(
    deviceTypeFilteredPlaceholders: PlaceholdersDetails[],
    serviceId: string,
    metaTime: number,
    deviceType: AdConfigDeviceTypeEnum,
    omitCache = false
  ): Promise<PlaceholdersDetails[]> {
    const hasSuperPanelEnable = await this.extensionService.checkExtensionStatus(
      serviceId,
      ExtEnableDataEnum.SUPER_PANEL_ENABLED,
      omitCache
    );

    const hasTopPremiumEnable = await this.extensionService.checkExtensionStatus(
      serviceId,
      ExtEnableDataEnum.TOP_PREMIUM_ENABLED,
      omitCache
    );

    const showTopPremium =
      hasTopPremiumEnable &&
      (await this.extensionService.checkExtensionScheduleStatus(
        serviceId,
        ExtScheduleDataEnum.TOP_PREMIUM_SCHEDULE,
        metaTime,
        omitCache
      ));

    const extensionFilteredPlaceholders = deviceTypeFilteredPlaceholders.filter(
      p =>
        (hasSuperPanelEnable || p.id !== 'superpanel') &&
        (showTopPremium || p.id !== 'top_premium')
    );

    if (ENV.LOG_FILTERED_PLACEHOLDERS === 'ENABLED') {
      log(
        'ADCONFIGS_SUPERPANEL_AND_TOP_PREMIUM_FILTERED_AMOUNT',
        {
          extensionFilteredPlaceholders: extensionFilteredPlaceholders.length.toString()
        },
        LogLevel.dev
      );
    }

    if (!extensionFilteredPlaceholders.length) {
      const errMsg = `WARN_${CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_SUPERPANEL_AND_TOP_PREMIUM}_CANNOT_FIND_ANY_ADCONFIGS_FOR_GIVEN_SUPERPANEL`;
      log(errMsg, { deviceType }, LogLevel.warn);

      throw CreateException({
        message: `Cannot find any adConfigs for superPanel or topPremium config`,
        statusCode: CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_SUPERPANEL_AND_TOP_PREMIUM
      });
    }
    return extensionFilteredPlaceholders;
  }
}

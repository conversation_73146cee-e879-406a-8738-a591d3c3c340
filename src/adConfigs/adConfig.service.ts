import { HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  AdConfig,
  AdConfigDeviceTypeEnum,
  AdConfigDocument,
  CachePartsEnum,
  LogLevel
} from 'ads-layouts-tools';
import { deHydrateDocuments } from 'Helpers';
import { AdConfigKey, IFilteredAdConfigs } from 'InterfacesAndTypes';
import { Model } from 'mongoose';
import { CreateException, log } from 'Utils';
import { CacheService } from '../cacheModule/cache.service';
import { ExtensionService } from '../extensionConfig/extension.service';
import { AdConfigHelper } from './adConfig.helper';

@Injectable()
export class AdConfigService {
  private adConfigHelper;

  constructor(
    @InjectModel(AdConfig.name)
    private AdConfigModel: Model<AdConfigDocument>,
    private readonly extensionService: ExtensionService,
    private readonly cache: CacheService
  ) {
    this.adConfigHelper = new AdConfigHelper(extensionService);
  }

  // READ
  async getAllAdConfigs(): Promise<AdConfigDocument[]> {
    const AdConfigData = await this.AdConfigModel.find({}).exec();
    if (!AdConfigData || AdConfigData.length == 0) {
      throw CreateException({
        message: 'AdConfigs data not found!',
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    return AdConfigData;
  }

  async selectPlaceholdersConfig(
    serviceId: string,
    deviceType: AdConfigDeviceTypeEnum,
    release: string,
    pageType: string,
    metaTime: number,
    sectionId?: string,
    pageId?: string | null,
    omitCache = false
  ): Promise<IFilteredAdConfigs> {
    log(
      'CONFIG_INVOKE_SELECT',
      {
        serviceId,
        deviceType,
        release,
        pageType,
        sectionId,
        pageId,
        omitCache
      },
      LogLevel.dev
    );

    const filterAdConfigs = this.adConfigHelper.partialFilterAdConfigs(
      serviceId,
      metaTime,
      deviceType,
      release,
      pageType,
      sectionId,
      pageId,
      omitCache
    );

    const searchServiceId = this.adConfigHelper.formatServiceId(serviceId);

    const key: AdConfigKey = `${CachePartsEnum.AD_CONFIGS}__${searchServiceId}__${pageType}__${
      release.endsWith('/') ? release.slice(0, -1) : release
    }`;

    let adConfigDocuments = await this.cache.get(key, omitCache);

    if (!adConfigDocuments) {
      adConfigDocuments = await this.AdConfigModel.find({
        'adsLayoutsAdditionalData.releaseServices': searchServiceId,
        'adsLayoutsAdditionalData.releaseName': release,
        pageType: { $in: [pageType] }
      });

      log(
        'ALL_ADS_CONFIG_DATA_LENGTH',
        { adsConfigs: adConfigDocuments.length },
        LogLevel.dev
      );

      if (adConfigDocuments.length > 0) {
        await this.cache.set(key, adConfigDocuments, omitCache);
      }
    }

    const adConfigs = deHydrateDocuments(adConfigDocuments);

    if (adConfigs.length === 0) {
      this.adConfigHelper.logAndThrowConfigNotFound(
        serviceId,
        release,
        pageType,
        sectionId,
        pageId
      );
    }

    return await this.adConfigHelper.selectBestMatchingConfig(
      adConfigs,
      sectionId,
      pageId,
      filterAdConfigs,
      release,
      serviceId
    );
  }
}

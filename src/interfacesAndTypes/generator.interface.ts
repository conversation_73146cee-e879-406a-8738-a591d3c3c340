import {
  ContentMeta,
  ICommonConfigFields,
  IRulesPriorities,
  PlaceholderType,
  RuleStats
} from 'InterfacesAndTypes';
import { Event, IPlaceholdersDetails, ParamsModel, Prettify, Rule } from 'ads-layouts-tools';

// Request
export enum PageTypeEnum {
  article = 'article',
  section = 'section'
}

export interface IDebugData {
  releaseVersion: string;
  allAvailableAdConfigGroups: string[];
  allRulesCount: number;
  successConditionsCount: number;
  successEventsCount: number;
  successMergeCount: number;
  shortSuccessMergeStats: {
    placeholderId: string;
    group: string;
    groupId: string;
  }[];
  rulesStats: { success: RuleStats[]; fail: RuleStats[] };
  reqBodyType: PageTypeEnum;
  fullConfigName: string;
}

export interface IAdsConfigIdentificationData {
  configName: string;
  serviceId: string[];
  modifiedDate: string;
  rulesPackage: string;
  releaseVersion: string;
  appVersion: string;
}

export interface IGeneratorResponse extends ICommonConfigFields {
  debugData?: IDebugData;
  requestMeta: ContentMeta;
  placeholders: IRulesPriorities[];
  version: string;
  adsConfigIdentificationData: IAdsConfigIdentificationData;
}

export type IMatchedPlaceholdersWithAdConfigs = Prettify<
  PlaceholderType &
    Pick<ParamsModel, 'priorityGroup'> &
    Omit<IPlaceholdersDetails, 'id' | 'width' | 'height'> & {
      eventPriority: ParamsModel['priority'];
      rulePriority: Rule['priority'];
      configId: string;
      width: string | number;
      height: string | number;
    }
>;

export interface IEngineSetupResponse {
  allFactsNames: string[];
  rulesPackage?: string;
}

export interface IMatchedPlaceholdersToEvents {
  successfulEvent: Event;
  siteMapMatchedPlaceholders: PlaceholderType[];
  ruleName: string;
  rulePriority: number;
}

export interface IDebugOptions {
  debug: boolean;
  omitCache: boolean;
}

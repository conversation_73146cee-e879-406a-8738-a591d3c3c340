import { AdConfigActivationThresholds, IPlaceholdersDetails } from 'ads-layouts-tools';

export interface ICommonConfigFields {
  masterId: string;
  bgPlugSrc: string | null;
  activationThresholds: AdConfigActivationThresholds;
  trafficCategory: string[];
}

export interface IFilteredAdConfigs {
  placeholders: IPlaceholdersDetails[];
  commonConfigFields: ICommonConfigFields;
  configName: string;
  serviceIdFromConfig: string[];
  modifiedDate: string;
}

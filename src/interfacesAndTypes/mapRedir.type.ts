import { AdConfig, Prettify } from 'ads-layouts-tools';
import { IFilteredAdConfigs } from './adConfig.interface';
import { ContentMeta } from './body.interface';

export type SDKDisplayResponse = {
  version: string;
  items: SDKDisplayItem[];
};

export type SDKDisplayItem = Prettify<
  {
    cfgLocation: string;
  } & Pick<AdConfig, 'pageType' | 'pageId' | 'section'>
>;

export type MapRedirResult = {
  selectedConfig: IFilteredAdConfigs;
  releaseVersion: string;
};

export type MapRedirFilterInput = Pick<
  ContentMeta,
  'locationInfoPageType' | 'locationInfoSectionId' | 'locationInfoPageId'
>;

export type MapRedirPrepareInput = Prettify<
  MapRedirFilterInput & Pick<ContentMeta, 'deviceType'>
>;

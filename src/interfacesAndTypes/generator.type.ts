import { IPlaceholdersConfig } from 'ads-layouts-tools';
import {
  BodyToHeaderParse,
  ContentMeta,
  IMatchedPlaceholdersWithAdConfigs,
  PlaceholderType
} from 'InterfacesAndTypes';

type PostmanHeaders = {
  'postman-token'?: string;
};

type ClientHeaders = {
  accept?: string;
  connection?: string;
  host?: string;
  'accept-encoding'?: string;
  'cache-control'?: string;
  'content-type'?: string;
  'content-length'?: string;
  'user-agent'?: string;
};

export type GeneratorHeaders = object &
  ClientHeaders &
  PostmanHeaders &
  BodyToHeaderParse<ContentMeta>;

type IMergeSuccess = {
  isSuccessfulMerge: true;
  mergeContent: IMatchedPlaceholdersWithAdConfigs[];
};
type IMergeFailure = {
  isSuccessfulMerge: false;
  unmergedPlaceholders?: { placeholders: PlaceholderType; eventAdConfigGroup: string };
};

export type IMergeResult = IMergeSuccess | IMergeFailure;

export type IRulesPriorities = Omit<
  IMatchedPlaceholdersWithAdConfigs,
  'eventPriority' | 'rulePriority' | 'priorityGroup'
>;

export class GeneratorQuery {
  debug?: string;
  omitCache?: string;
}

export type ValidMapRedir = {
  url: string;
  prefix: string;
  serviceId: string;
};

export interface IPlaceholdersConfigWithVersion extends IPlaceholdersConfig {
  version: string;
}

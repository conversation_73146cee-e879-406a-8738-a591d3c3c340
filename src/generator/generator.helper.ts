import {
  AdConfigOverride,
  LogLevel,
  IPlaceholdersDetails,
  returnAsArrayEmpty
} from 'ads-layouts-tools';
import { returnAsArray } from 'Helpers';
import {
  IMatchedPlaceholdersToEvents,
  IMatchedPlaceholdersWithAdConfigs,
  IMergeResult,
  PlaceholderType,
  RuleStats
} from 'InterfacesAndTypes';
import { log } from 'Utils';

export class GeneratorHelperClass {
  public mergeSiteMapPlaceholdersWithAdConfigs = (
    adConfigGroup: string,
    adConfigOverride: AdConfigOverride | undefined,
    eventPriority: number | undefined,
    rulePriority: number,
    priorityGroup: string | undefined,
    siteMapMatchedPlaceholders: PlaceholderType,
    adsConfigs: IPlaceholdersDetails[]
  ): IMergeResult => {
    let eventAdConfigGroup = '';

    if (adConfigGroup) {
      eventAdConfigGroup = adConfigGroup;

      const availableAdConfigs = adsConfigs?.filter(
        pc => pc.AD_Config_group === eventAdConfigGroup
      );

      if (availableAdConfigs?.length) {
        return {
          isSuccessfulMerge: true,
          mergeContent: this.matchPlaceholdersWithAdConfigs(
            siteMapMatchedPlaceholders,
            availableAdConfigs,
            adConfigOverride,
            eventPriority || 1,
            rulePriority,
            priorityGroup
          )
        };
      }
    }
    return {
      isSuccessfulMerge: false,
      unmergedPlaceholders: {
        placeholders: siteMapMatchedPlaceholders,
        eventAdConfigGroup
      }
    };
  };

  public updateRuleStatsWithMergeResult(
    stats: RuleStats[],
    ruleName: string,
    mergeResult: IMergeResult
  ): RuleStats[] {
    return stats.map(stat => {
      if (stat.ruleName !== ruleName) return stat;

      return {
        ...stat,
        mergePass: mergeResult.isSuccessfulMerge,
        mergeResult: mergeResult.isSuccessfulMerge
          ? mergeResult.mergeContent?.map(
              ({ id, type, AD_Config_group, AD_Config_element_id }) => ({
                id,
                type,
                AD_Config_group,
                AD_Config_element_id
              })
            )
          : mergeResult.unmergedPlaceholders
      };
    });
  }

  public sortSuccessfulPlaceholders = (
    foundPlaceholdersAndEvents: IMatchedPlaceholdersToEvents[]
  ) => {
    const eachPlaceholderWithEventDataFlat = foundPlaceholdersAndEvents.flatMap(el => {
      const eventData = {
        adConfigGroup: el.successfulEvent.params.adConfigGroup,
        adConfigOverride: el.successfulEvent.params.adConfigOverride,
        eventPriority: el.successfulEvent.params.priority,
        priorityGroup: el.successfulEvent.params.priorityGroup,
        ruleName: el.ruleName,
        rulePriority: el.rulePriority
      };

      return returnAsArray(el.siteMapMatchedPlaceholders).map(placeholder => ({
        ...eventData,
        placeholder
      }));
    });

    const uniqAdConfigGroups: string[] = [
      ...new Set(eachPlaceholderWithEventDataFlat.map(el => el.adConfigGroup))
    ];

    const sortedPlaceholdersPerGroupById = uniqAdConfigGroups
      .map(groupName => {
        return eachPlaceholderWithEventDataFlat
          .filter(item => item.adConfigGroup === groupName)
          .sort((a, b) => {
            if (a.placeholder && b.placeholder) {
              if (+a.placeholder.id === +b.placeholder.id) {
                return b.rulePriority - a.rulePriority;
              }
              return +a.placeholder.id - +b.placeholder.id;
            }

            if (a.placeholder) return -1;
            if (b.placeholder) return 1;
            return 0;
          });
      })
      .flat();

    return sortedPlaceholdersPerGroupById;
  };

  private matchPlaceholdersWithAdConfigs = (
    siteMapMatchedPlaceholders: PlaceholderType,
    availableAdConfigs: IPlaceholdersDetails[],
    ruleAdConfigOverride: AdConfigOverride | undefined,
    eventPriority: number,
    rulePriority: number,
    priorityGroup: string | undefined
  ): IMatchedPlaceholdersWithAdConfigs[] => {
    const placeholders: IMatchedPlaceholdersWithAdConfigs[] = [];

    const placeholderArray = returnAsArrayEmpty(siteMapMatchedPlaceholders); // Either length 1 or 0

    if (placeholderArray.length === 0) {
      return [];
    }

    const sortConfigs = (a: IPlaceholdersDetails, b: IPlaceholdersDetails): number => {
      if (+a.AD_Config_element_id < +b.AD_Config_element_id) return -1;
      if (+a.AD_Config_element_id > +b.AD_Config_element_id) return 1;
      return 0;
    };

    const sortedAdConfigs = availableAdConfigs.sort((a, b) => sortConfigs(a, b));

    for (let i = 0; i < placeholderArray.length && i < sortedAdConfigs?.length; i++) {
      const { id, width, height, ...otherSortedAdConfigs } = sortedAdConfigs[i];

      if (width === null || height === null) {
        log(
          'ERROR_CANNOT_MERGE_ADCONFIG_WITH_NULL_HEIGHT_OR_WIDTH',
          {
            adConfig: sortedAdConfigs[i],
            placeholder: siteMapMatchedPlaceholders
          },
          LogLevel.error
        );
        continue;
      }

      placeholders.push({
        ...siteMapMatchedPlaceholders,
        configId: id,
        ...otherSortedAdConfigs,
        width,
        height,
        ...ruleAdConfigOverride,
        eventPriority,
        rulePriority,
        priorityGroup
      });
    }

    return placeholders;
  };
}

import { HttpStatus, Injectable, PipeTransform } from '@nestjs/common';
import { AdConfigDeviceTypeEnum, LogLevel, ServiceEnvEnum } from 'ads-layouts-tools';
import {
  requireStringValues,
  returnAsArrayEmpty,
  validateAccessModel,
  validatePaywall
} from 'Helpers';
import {
  CommonRequest,
  CommonRequestToCheck,
  ContentMeta,
  ContentMetaRequiredFields,
  ValidMapRedir
} from 'InterfacesAndTypes';
import * as Joi from 'joi';
import { CreateException, log } from 'Utils';
import { CacheService } from '../../cacheModule/cache.service';
import { ENV } from '../../envalidConfig';

const mapRedirPattern =
  /^(?<prefix>.+\/)sdk-display\/configs\/[\w]+\/[\d\.]+\/(?<serviceId>\w+)_map\.json$/;

@Injectable()
export class RequestBodyValidationPipe implements PipeTransform {
  constructor(private readonly cache: CacheService) {}

  public async transform(value: CommonRequestToCheck): Promise<CommonRequest> {
    const serviceIdArrayValidationEnv = ENV.SERVICE_ID_ARRAY_VALIDATION;
    let validServiceIds: string[] = [];

    if (serviceIdArrayValidationEnv === 'ENABLED') {
      const validServiceIdsEnv = ENV.VALID_SERVICE_IDS;
      const localCacheServiceIds = await this.cache.getServiceIds();

      validServiceIds = localCacheServiceIds ?? returnAsArrayEmpty(validServiceIdsEnv);
    }

    if (!value) {
      log('ERROR_GENERATOR_NO_BODY_CONTENT', { value }, LogLevel.error);
      throw CreateException({
        message: 'Request Body is required',
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    const result = Joi.object<CommonRequestToCheck>({
      ...requireStringValues<CommonRequestToCheck>(['type']),
      direction: Joi.string(),
      meta: Joi.object<ContentMeta>({
        ...requireStringValues<ContentMetaRequiredFields>([
          'locationInfoPageType',
          'locationInfoSectionId',
          'locationInfoSectionName',
          'siteVersionIdentifier',
          'time'
        ]),
        locationInfoPageId: Joi.string().allow('', null),
        accessModel: Joi.string().custom(validateAccessModel()),
        paywall: Joi.string().custom(validatePaywall()),
        siteVersion: Joi.string().allow(''),
        serviceId: Joi.string()
          .valid(...validServiceIds)
          .allow('')
          .required(),
        serviceEnv: Joi.alternatives()
          .try(
            Joi.string().valid(...Object.values(ServiceEnvEnum)),
            Joi.string().regex(new RegExp(`^${ServiceEnvEnum.STAGE}\\d+$`)),
            Joi.string().regex(/^prev-.*/)
          )
          .error(errors => {
            const defaultServiceEnv = Object.values(ServiceEnvEnum).join(', ');
            errors[0].message = `serviceEnv should be one of <${defaultServiceEnv}> or match pattern 'prev-*', or match pattern '^${ServiceEnvEnum.STAGE}\\d+$'`;

            return errors[0];
          }),
        deviceType: Joi.string()
          .valid(...Object.values(AdConfigDeviceTypeEnum))
          .required(),
        rulesPackage: Joi.string(),
        serviceName: Joi.string()
      }).required(),
      elements: Joi.array().required(),
      mapRedir: Joi.string().uri().regex(mapRedirPattern).optional()
    })
      .options({
        abortEarly: false
      })
      .validate(value);

    if (result.error) {
      const errorMessages = result.error?.details?.map(d => d.message).join();

      log('ERROR_BODY_VALIDATION_ERROR', { err: errorMessages }, LogLevel.error);

      throw CreateException({
        message: `Body validation error. Errors: ${errorMessages}`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    const transformedValue = this.extendWithMapRedir(value);

    log('REQUEST_BODY_VALIDATION_PASSED', { bodyValid: true }, LogLevel.dev);

    return transformedValue;
  }

  extendWithMapRedir(value: CommonRequestToCheck): CommonRequest {
    if (!value.mapRedir) {
      return value as CommonRequest;
    }

    const match = value.mapRedir.match(mapRedirPattern);

    if (!match || !match.groups) {
      // This situation should not occur because Joi validation already checked the pattern, but keep additional guard
      log('ERROR_MAP_REDIR_PATTERN_MISMATCH', { mapRedir: value.mapRedir }, LogLevel.error);
      throw CreateException({
        message: 'Invalid mapRedir format',
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    const mapRedirObj: ValidMapRedir = {
      url: value.mapRedir,
      prefix: match.groups.prefix,
      serviceId: match.groups.serviceId
    };

    return { ...value, mapRedir: mapRedirObj };
  }
}

import { HttpException, Injectable } from '@nestjs/common';
import {
  AdConfigDeviceTypeEnum,
  CustomHttpStatus,
  LogLevel,
  IPlaceholdersDetails
} from 'ads-layouts-tools';

import * as dayjs from 'dayjs';
import { normalizeResponseVersionField, removePrioParams, returnAsArray } from 'Helpers';
import {
  CommonRequest,
  ICommonConfigFields,
  IDebugData,
  IDebugOptions,
  IEngineResult,
  IFilteredAdConfigs,
  IGeneratorResponse,
  IMatchedPlaceholdersToEvents,
  IMatchedPlaceholdersWithAdConfigs,
  IPlaceholdersConfigWithVersion,
  IRuleResult,
  IRulesPriorities,
  MapRedirFilterInput,
  MapRedirPrepareInput,
  MapRedirResult,
  MergePlaceholdersWithAdConfigsBaseOnEventsResult,
  PlaceholderType,
  RuleStats,
  SDKDisplayItem,
  SDKDisplayResponse,
  ValidMapRedir
} from 'InterfacesAndTypes';

import fetch from 'node-fetch';
import { CreateException, log } from 'Utils';
import { AdConfigService } from '../adConfigs/adConfig.service';
import { DisplayConfigService } from '../displayConfig/displayConfig.service';
import { EventsService } from '../events/events.service';
import { RuleService } from '../rules/rules.service';
import { RulesEngine } from '../rulesEngine/rulesEngine';
import { ServiceToPackageMapService } from '../serviceToPackageMap/serviceToPackageMap.service';
import { GeneratorHelperClass } from './generator.helper';

const getResponse = async <T>(url: string): Promise<T> => {
  try {
    const res = await fetch(url);
    return (await res.json()) as T;
  } catch (cause) {
    const { message, stack } = cause instanceof Error ? cause : new Error('Unknown error');

    throw CreateException({
      message: `Failed to fetch data from url: ${url}, error: ${message}, stack: ${stack}`,
      statusCode: 400
    });
  }
};

@Injectable()
export class GeneratorService extends RulesEngine {
  constructor(
    public readonly ruleService: RuleService,
    public readonly adConfigService: AdConfigService,
    public readonly displayConfigService: DisplayConfigService,
    public readonly serviceToPackageMapService: ServiceToPackageMapService,
    public readonly eventService: EventsService,
    public readonly generatorHelper: GeneratorHelperClass
  ) {
    super(ruleService, serviceToPackageMapService);
  }

  filterAdConfigs(
    fileItems: SDKDisplayItem[],
    metaArgs: MapRedirFilterInput
  ): SDKDisplayItem | undefined {
    const {
      locationInfoPageType: metaPageType,
      locationInfoSectionId: metaSectionId,
      locationInfoPageId: metaPageId
    } = metaArgs;

    const pageTypeItems = fileItems.filter(({ pageType }) => pageType.includes(metaPageType));

    if (metaSectionId && metaPageId) {
      const result = pageTypeItems.find(
        c => c.section.some(s => s.id === metaSectionId) && c.pageId.includes(metaPageId)
      );

      if (result) {
        return result;
      }
    }

    if (metaSectionId) {
      const result = pageTypeItems.find(c => c.section.some(s => s.id === metaSectionId));

      if (result) {
        return result;
      }
    }

    if (metaPageId) {
      const result = pageTypeItems.find(c => c.pageId.includes(metaPageId));

      if (result) {
        return result;
      }
    }

    return pageTypeItems[0];
  }

  async getPlaceholdersConfigFromURL(
    params: ValidMapRedir,
    metaArgs: MapRedirPrepareInput
  ): Promise<MapRedirResult> {
    const { url, prefix, serviceId } = params;

    const { locationInfoPageType, locationInfoSectionId, locationInfoPageId } = metaArgs;

    const mapRedirFile = await getResponse<SDKDisplayResponse>(url);

    const chosenItem = this.filterAdConfigs(mapRedirFile.items, {
      locationInfoPageType,
      locationInfoSectionId,
      locationInfoPageId
    });

    if (!chosenItem) {
      throw CreateException({
        message: `Config not found in map items. Not matching locationInfoPageType:${locationInfoPageType}, locationInfoSectionId:${locationInfoSectionId}, locationInfoPageId:${locationInfoPageId}`,
        statusCode: 404
      });
    }

    const nextUrl = `${prefix}${chosenItem.cfgLocation}`;

    const placeholdersConfig = await getResponse<IPlaceholdersConfigWithVersion>(nextUrl);

    return this.prepareResult(placeholdersConfig, metaArgs, serviceId);
  }

  prepareResult(
    placeholdersConfig: IPlaceholdersConfigWithVersion,
    metaArgs: MapRedirPrepareInput,
    serviceId: string
  ): MapRedirResult {
    const { deviceType, locationInfoPageType } = metaArgs;
    const { masterId, bgPlugSrc, activationThresholds, trafficCategory, version } =
      placeholdersConfig;

    const commonConfigFields: ICommonConfigFields = {
      masterId,
      bgPlugSrc,
      activationThresholds,
      trafficCategory
    };
    const placeholders = placeholdersConfig.placeholders.filter(p =>
      p.deviceType.includes(deviceType)
    );

    const selectedConfig: IFilteredAdConfigs = {
      placeholders,
      commonConfigFields,
      configName: locationInfoPageType,
      serviceIdFromConfig: [serviceId],
      modifiedDate: 'mapRedir' // ?
    };

    const releaseVersion = version.endsWith('/') ? version : `${version}/`;

    return { selectedConfig, releaseVersion };
  }

  async createResponse(
    content: CommonRequest,
    debugMode: boolean,
    omitCache = false,
    mapRedir?: ValidMapRedir
  ): Promise<IGeneratorResponse> {
    const appVersion = process.env.npm_package_version || 'Version not found';

    if (omitCache) {
      // update variant weights if cache is omitted to have the most recent data with no cache
      this.eventService.updateVariantWeights(true);
    }

    const {
      meta: {
        deviceType,
        serviceId,
        serviceEnv,
        siteVersion,
        locationInfoPageType,
        locationInfoSectionId,
        locationInfoPageId,
        time
      }
    } = content;
    let selectedConfig: IFilteredAdConfigs | null = null;
    let releaseVersion: string | null = null;
    if (mapRedir) {
      log('MAP_REDIR', { mapRedirUrl: mapRedir?.url }, LogLevel.dev);

      const mapRedirResult = await this.getPlaceholdersConfigFromURL(mapRedir, content.meta);

      releaseVersion = mapRedirResult.releaseVersion;
      selectedConfig = mapRedirResult.selectedConfig;
    } else {
      releaseVersion = await this.displayConfigService.getReleaseVersion(
        serviceId,
        time,
        serviceEnv,
        siteVersion,
        omitCache
      );

      selectedConfig = await this.adConfigService.selectPlaceholdersConfig(
        serviceId,
        deviceType,
        releaseVersion,
        locationInfoPageType,
        parseInt(time),
        locationInfoSectionId,
        locationInfoPageId,
        omitCache
      );
    }

    const { allFactsNames, rulesPackage } = await this.engineSetup(content, omitCache);

    const engineRunResult = await this.getRulesEngineRunResult(content);

    const { successfulPlaceholdersMerge, failedPlaceholdersMerge, rulesStats } =
      await this.mergePlaceholdersWithAdConfigsBaseOnEvents(
        engineRunResult,
        allFactsNames,
        selectedConfig.placeholders,
        deviceType
      );

    const placeholdersWithHandledPrios = this.handleRulesPriorities(
      successfulPlaceholdersMerge
    );

    const debugData = this.handleRequestProcessingStats(
      placeholdersWithHandledPrios,
      failedPlaceholdersMerge,
      content,
      releaseVersion,
      selectedConfig.configName,
      selectedConfig.placeholders,
      rulesStats
    );

    return {
      debugData: debugMode ? debugData : undefined,
      requestMeta: content.meta,
      ...selectedConfig.commonConfigFields,
      placeholders: placeholdersWithHandledPrios,
      version: normalizeResponseVersionField(releaseVersion),
      adsConfigIdentificationData: {
        configName: selectedConfig.configName,
        serviceId: selectedConfig.serviceIdFromConfig,
        modifiedDate: dayjs(selectedConfig.modifiedDate).format('YYYY-MM-DD HH:mm:ss'),
        rulesPackage: rulesPackage || 'original rules (no rules package assigned)',
        releaseVersion,
        appVersion
      }
    };
  }

  private getRulesEngineRunResult = async (content: CommonRequest): Promise<IEngineResult> => {
    try {
      const result = (await this.engine.run(content)) as IEngineResult;

      log(
        'ENGINE_RULES_STATUS',
        {
          rules: result.almanac.ruleResults.map(el => ({
            name: el.name,
            conditionResult: el.result
          }))
        },
        LogLevel.dev
      );
      const successfulEvents = result.almanac.events.success;

      if (!successfulEvents.length) {
        log(
          `WARN_${CustomHttpStatus.LACK_OF_SUCCESSFUL_RULES}_LACK_OF_SUCCESSFUL_RULES`,
          {},
          LogLevel.warn
        );

        throw CreateException(
          {
            message: `Lack of successful rules (check rules conditions)`,
            statusCode: CustomHttpStatus.LACK_OF_SUCCESSFUL_RULES
          },
          undefined,
          '_getRulesEngineRunResult'
        );
      }

      return result;
    } catch (err) {
      if (err instanceof HttpException) {
        throw err;
      }

      log('ERROR_ENGINE_RUN_RESULT', { err }, LogLevel.error);

      throw CreateException({
        message: `Unknown Json Rules Engine result error. ${err}`,
        statusCode: CustomHttpStatus.ENGINE_RUN
      });
    }
  };

  private mergePlaceholdersWithAdConfigsBaseOnEvents = async (
    engineRunResult: IEngineResult,
    allFactsNames: string[],
    adsConfigs: IPlaceholdersDetails[],
    deviceType: AdConfigDeviceTypeEnum
  ): Promise<MergePlaceholdersWithAdConfigsBaseOnEventsResult> => {
    const successfulPlaceholdersMerge: IMatchedPlaceholdersWithAdConfigs[] = [];
    const failedPlaceholdersMerge: PlaceholderType[] = [];

    const allRulesStatus = engineRunResult.almanac.ruleResults;
    const successfulRules = allRulesStatus.filter(rule => rule.result);

    let rulesStats: RuleStats[] = allRulesStatus.map(rule => ({
      ruleName: rule.name,
      conditionPass: rule.result
    }));

    const resultsWithStats = await this.findMatchingPlaceholdersForRules(
      successfulRules,
      engineRunResult,
      allFactsNames,
      deviceType,
      rulesStats
    );

    const matchedPlaceholders = resultsWithStats.matchedPlaceholdersResult;

    rulesStats = resultsWithStats.updatedRulesStats;

    const sortedPlaceholders =
      this.generatorHelper.sortSuccessfulPlaceholders(matchedPlaceholders);

    for (const item of sortedPlaceholders) {
      const {
        adConfigGroup,
        adConfigOverride,
        eventPriority,
        rulePriority,
        priorityGroup,
        placeholder,
        ruleName
      } = item;

      const mergeResult = this.generatorHelper.mergeSiteMapPlaceholdersWithAdConfigs(
        adConfigGroup,
        adConfigOverride,
        eventPriority,
        rulePriority,
        priorityGroup,
        placeholder,
        adsConfigs
      );

      rulesStats = this.generatorHelper.updateRuleStatsWithMergeResult(
        rulesStats,
        ruleName,
        mergeResult
      );

      if (mergeResult.isSuccessfulMerge) {
        successfulPlaceholdersMerge.push(...mergeResult.mergeContent);
        adsConfigs = this.removeUsedAdConfigs(adsConfigs, successfulPlaceholdersMerge);
      } else if (mergeResult.unmergedPlaceholders?.placeholders) {
        failedPlaceholdersMerge.push(mergeResult.unmergedPlaceholders.placeholders);
      }
    }

    return {
      successfulPlaceholdersMerge,
      failedPlaceholdersMerge,
      rulesStats
    };
  };

  private async findMatchingPlaceholdersForRules(
    successfulRules: IRuleResult[],
    engineRunResult: IEngineResult,
    allFactsNames: string[],
    deviceType: AdConfigDeviceTypeEnum,
    rulesStats: RuleStats[]
  ): Promise<{
    matchedPlaceholdersResult: IMatchedPlaceholdersToEvents[];
    updatedRulesStats: RuleStats[];
  }> {
    const matchedPlaceholdersResult: IMatchedPlaceholdersToEvents[] = [];

    for (const rule of successfulRules) {
      const { name: ruleName, event: successfulEvent, priority: rulePriority } = rule;

      const siteMapMatchedPlaceholders =
        await this.eventService.getSiteMapPlaceholdersBasedOnEvents(
          successfulEvent!,
          engineRunResult.almanac,
          allFactsNames,
          deviceType
        );

      rulesStats = rulesStats.map(el => {
        if (el.ruleName === ruleName) {
          return {
            ...el,
            eventPass: returnAsArray(siteMapMatchedPlaceholders).some(el => !!el),
            selectedPlaceholders: siteMapMatchedPlaceholders
          };
        }
        return el;
      });

      log(
        'SITEMAP_EVENTS_MATCHED_PLACEHOLDERS',
        { ruleName, siteMapMatchedPlaceholders },
        LogLevel.dev
      );

      matchedPlaceholdersResult.push({
        ruleName,
        successfulEvent: successfulEvent!,
        siteMapMatchedPlaceholders,
        rulePriority: rulePriority!
      });
    }

    return { matchedPlaceholdersResult, updatedRulesStats: rulesStats };
  }

  private handleRequestProcessingStats = (
    placeholdersWithHandledPrios: IRulesPriorities[],
    failedPlaceholdersMerge: PlaceholderType[],
    content: CommonRequest,
    releaseVersion: string,
    configName: string,
    adsConfigs: IPlaceholdersDetails[],
    rulesStats: RuleStats[]
  ): IDebugData => {
    if (placeholdersWithHandledPrios.length === 0 && failedPlaceholdersMerge.length > 0) {
      const errMsg = `WARN_${CustomHttpStatus.CANNOT_MERGE_ANY_ADS_CONFIGS}_CANNOT_MERGE_ANY_ADS_CONFIGS_INTO_PLACEHOLDERS`;
      log(errMsg, { failedPlaceholdersMerge }, LogLevel.warn);
      throw CreateException({
        message: `Cannot merge any ads configs with placeholders`,
        statusCode: CustomHttpStatus.CANNOT_MERGE_ANY_ADS_CONFIGS
      });
    }

    const allRulesCount = rulesStats.length;

    const { successConditionsCount, successEventsCount, successMergeCount } =
      rulesStats.reduce(
        (acc, curr) => {
          if (curr.conditionPass) acc.successConditionsCount++;
          if (curr.eventPass) acc.successEventsCount++;
          if (curr.mergePass) acc.successMergeCount++;
          return acc;
        },
        { successConditionsCount: 0, successEventsCount: 0, successMergeCount: 0 }
      );

    const logMsg = `RESPONSE_STATS_OK_${successMergeCount}_FAIL_${allRulesCount - successMergeCount}_${
      content.meta.serviceId
    }_${content.meta.deviceType}_${content.type}_${
      content.meta.locationInfoSectionId
    }_${releaseVersion.replace('/', '').replaceAll('.', '_')}`;

    const rulesStatsFormatted = rulesStats.reduce(
      (acc: { success: RuleStats[]; fail: RuleStats[] }, el) => {
        acc[el.mergePass ? 'success' : 'fail'].push(el);

        return acc;
      },
      { success: [], fail: [] }
    );

    const debugData: IDebugData = {
      releaseVersion,
      allAvailableAdConfigGroups: adsConfigs.map(
        ac => `group: ${ac.AD_Config_group}, id: ${ac.AD_Config_element_id}`
      ),
      allRulesCount,
      successConditionsCount,
      successEventsCount,
      successMergeCount,
      shortSuccessMergeStats: placeholdersWithHandledPrios.map(p => ({
        placeholderId: p.id,
        group: p.AD_Config_group,
        groupId: p.AD_Config_element_id
      })),
      rulesStats: rulesStatsFormatted,
      reqBodyType: content.type,
      fullConfigName: configName
    };

    if (failedPlaceholdersMerge.length > 0) {
      log(`WARN_${logMsg}`, { debugData }, LogLevel.warn);
    } else {
      log(`SUCCESS_${logMsg}`, { debugData });
    }

    return debugData;
  };

  private removeUsedAdConfigs = (
    adsConfigs: IPlaceholdersDetails[],
    successfulPlaceholdersMerge: IMatchedPlaceholdersWithAdConfigs[]
  ): IPlaceholdersDetails[] =>
    adsConfigs.filter(ac => {
      const shouldKeep = !successfulPlaceholdersMerge.some(
        fp =>
          fp.AD_Config_group === ac.AD_Config_group &&
          fp.AD_Config_element_id === ac.AD_Config_element_id
      );
      if (!shouldKeep) {
        log(
          'REMOVE_USED_ADCONFIG',
          {
            placeholderId: ac.id,
            AD_Config_group: ac.AD_Config_group,
            AD_Config_element_id: ac.AD_Config_element_id
          },
          LogLevel.dev
        );
      }
      return shouldKeep;
    });

  private handleRulesPriorities = (
    successfulPlaceholdersMerge: IMatchedPlaceholdersWithAdConfigs[]
  ): IRulesPriorities[] => {
    const hasPriorityGroup = successfulPlaceholdersMerge.some(fp => fp.priorityGroup);

    if (hasPriorityGroup) {
      const uniqPriorityGroups = [
        ...new Set(successfulPlaceholdersMerge.map(fp => fp.priorityGroup))
      ];

      const placeholdersWithoutPriorityGroup = successfulPlaceholdersMerge.filter(
        fp => !fp.priorityGroup
      );

      const resultPlaceholders = uniqPriorityGroups.map(priorityGroup => {
        return successfulPlaceholdersMerge
          .filter(fp => fp.priorityGroup === priorityGroup)
          .sort((a, b) => {
            if (a.rulePriority !== b.rulePriority) {
              return a.rulePriority - b.rulePriority;
            }
            return (a.eventPriority ?? 0) - (b.eventPriority ?? 0);
          })[0];
      });

      resultPlaceholders.push(...placeholdersWithoutPriorityGroup);

      return removePrioParams(resultPlaceholders);
    }

    return removePrioParams(
      successfulPlaceholdersMerge.reduce((acc: IMatchedPlaceholdersWithAdConfigs[], curr) => {
        const findEl = acc?.find(a => a.id === curr.id);

        if (!findEl) {
          acc?.push(curr);
        } else {
          if (curr.rulePriority < findEl.rulePriority) {
            const newAcc = acc.filter(a => a.id !== curr.id);

            return [...newAcc, curr];
          } else if (curr.rulePriority === findEl.rulePriority) {
            if ((curr.eventPriority ?? 0) < (findEl.eventPriority ?? 0)) {
              const newAcc = acc.filter(a => a.id !== curr.id);

              return [...newAcc, curr];
            }
          }
        }

        return acc;
      }, [])
    );
  };

  public retrieveDebugQueryParamOptions = (
    debugQueryParam: string | undefined
  ): IDebugOptions => {
    const debugIngredients = debugQueryParam?.split(',') || [];

    return {
      debug: debugIngredients[0] === 'true',
      omitCache: debugIngredients[1] === 'omitCache'
    };
  };
}

import { Module } from '@nestjs/common';
import { AdConfigModule } from '../adConfigs/adConfig.module';
import { DisplayConfigModule } from '../displayConfig/displayConfig.module';
import { EventsModule } from '../events/events.module';
import { RulesModule } from '../rules/rules.module';
import { ServiceToPackageMapModule } from '../serviceToPackageMap/serviceToPackageMap.module';
import { GeneratorController } from './generator.controller';
import { GeneratorHelperClass } from './generator.helper';
import { GeneratorService } from './generator.service';

@Module({
  imports: [
    RulesModule,
    AdConfigModule,
    DisplayConfigModule,
    ServiceToPackageMapModule,
    EventsModule,
    GeneratorHelperClass
  ],
  controllers: [GeneratorController],
  providers: [GeneratorService, GeneratorHelperClass]
})
export class GeneratorModule {}

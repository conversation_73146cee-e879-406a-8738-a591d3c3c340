import { KeysWithArrayValues, Rule } from 'ads-layouts-tools';

const NO_EMPTY = '!empty' as const;

type QueryValues =
  | { [key: string]: { $exists: false } }
  | { [key: string]: { $eq: [] } }
  | { [key: string]: { $elemMatch: { $in: string[]; $nin: string[] } } };

type mongoQueryType = {
  $or?: QueryValues[];
};

/**
 * Generates a mongoose part of a query object for a field.
 *
 * Query logic (MongoDB $or condition):
 * 1. When request has a non-empty value:
 *    - Matches rules where the field array contains exactly the requested values and does not contain any negated values (e.g. "!abc") or "!empty"
 *    - BUT excludes rules where the array contains the negation (!value) or !empty
 * 2. When request has an empty value:
 *    - Matches rules where the (field array is empty and does not contain ["!empty"]) or (field array has "all" and is not excluded by any negated value (e.g. "!abc") or "!empty")
 * 3. Always matches rules where:
 *    - if rule has "all" and is not excluded by any negated value (e.g. "!abc") or "!empty"
 *
 * Exclusion logic (stored in rule arrays, not in requests):
 * - Rules can use !<value> to exclude specific values (e.g., ['zoltan', '!k2'] excludes k2)
 * - Rules can use !empty to require non-empty values (e.g., ['k2', '!empty'] requires k2 and non-empty)
 * - Rules with [] or undefined field match everything (equivalent to 'all')
 *
 * Examples:
 * - request.siteVersion = "abc", rule.siteVersion = ["!abc"] => rule is NOT picked (excluded by !abc)
 * - request.siteVersion = "efg", rule.siteVersion = ["efg", "!abc"] => rule is picked, "!abc" is ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["efg", "!abc"] => rule is not picked, "!abc" is ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["efg", "abc"] => rule is  picked, exact match
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["abc"] => rule is not picked, insufficient match
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all"] => rule is picked, "all" is ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc"] => rule is not picked, "!abc" is ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!empty"] => rule is not picked, "!empty" is ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc", "!empty"] => rule is not picked, "!abc" and "!empty" are ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc", "!empty", "abc"] => rule is picked, "!abc" and "!empty" are ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc", "!empty", "abc", "efg"] => rule is picked, "!abc" and "!empty" are ignored
 * - request.siteVersion = "efg", rule.siteVersion = ["efg", "!abc"] => rule is picked (!abc ignored)
 * - request.siteVersion = "", rule.siteVersion = ["efg", "!empty"] => rule is NOT picked (excluded by !empty)
 * - request.siteVersion = "efg", rule.siteVersion = ["efg", "!empty"] => rule is picked (!empty ignored)
 * - request.siteVersion = "", rule.siteVersion = ["", "!empty"] => invalid never happens.
 * - request.siteVersion = "", rule.siteVersion = ["!empty"] => invalid never happens.
 * - request.siteVersion = "", rule.siteVersion = ["all", "!empty"] => rule is not picked (excluded by !empty)
 * - request.siteVersion = "<any valid value>", rule.siteVersion = ["all", "!empty"] => rule is picked (!empty ignored)
 *
 * @param fieldName - The name of the field to query.
 * @param valueToSearchFor - A comma-separated list of values to look for in the field of the document.
 * @returns A mongoose query object with $or conditions.
 */
export const queryFieldIncludesOrEmpty = (
  fieldName: KeysWithArrayValues<Rule>,
  valueToSearchFor: string
): mongoQueryType => {
  const conditions: QueryValues[] = [];

  if (valueToSearchFor) {
    // If request has a non-empty value, create a query to match rules that:
    // 1. Have ANY of the requested values in their array
    // 2. Do NOT have the negation of ANY requested values (e.g., if request has "k2", rule shouldn't have "!k2")
    // 3. Do NOT have "!empty" (since request is not empty)
    const splitValues = valueToSearchFor.split(',');
    const excluded = splitValues.map(v => `!${v}`);
    excluded.push(NO_EMPTY);

    conditions.push({ [fieldName]: { $elemMatch: { $in: splitValues, $nin: excluded } } });
  } else {
    // If request has an empty value, also match rules that have an array with empty string
    conditions.push({ [fieldName]: { $elemMatch: { $in: [''], $nin: [] } } });
  }

  // Always include rules with empty or undefined fields (matches "all" case - rules without restrictions)
  conditions.push({ [fieldName]: { $exists: false } }, { [fieldName]: { $eq: [] } }); // these line has to be adjusted to match the new logic. Always match rules with empty or undefined fields. Have to be changed to accommodate "all" | "!empty" cases.

  return { $or: conditions };
};

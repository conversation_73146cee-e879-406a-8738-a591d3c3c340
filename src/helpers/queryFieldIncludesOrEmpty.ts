import { KeysWithArrayValues, Rule } from 'ads-layouts-tools';

const NO_EMPTY = '!empty' as const;

type QueryValues =
  | { [key: string]: { $exists: false } }
  | { [key: string]: { $eq: [] } }
  | { [key: string]: { $elemMatch: { $in: string[]; $nin: string[] } } };

type mongoQueryType = {
  $or?: QueryValues[];
};

/**
 * Generates a mongoose part of a query object for a field.
 *
 * Query logic (MongoDB $or condition):
 * 1. When request has a non-empty value:
 *    - Matches rules where the field array contains exactly the requested values and does not contain any negated values (e.g. "!abc") or "!empty"
 *    - BUT excludes rules where the array contains the negation (!value) or !empty
 * 2. When request has an empty value:
 *    - Matches rules where the (field array is empty and does not contain ["!empty"]) or (field array has "all" and is not excluded by any negated value (e.g. "!abc") or "!empty")
 * 3. Always matches rules where:
 *    - if rule has "all" and is not excluded by any negated value (e.g. "!abc") or "!empty"
 *
 * Exclusion logic (stored in rule arrays, not in requests):
 * - Rules can use !<value> to exclude specific values (e.g., ['zoltan', '!k2'] excludes k2)
 * - Rules can use !empty to require non-empty values (e.g., ['k2', '!empty'] requires k2 and non-empty)
 * - Rules with [] field are treated as "empty" arrays. (not equivalent to 'all')
 * - Query for Rules with undefined field are not considered, because this function does not deal with undefined fields. Invocation of this function with undefined field will never happen.
 *
 * Examples:
 * - request.siteVersion = "abc", rule.siteVersion = ["!abc"] => rule is NOT picked (excluded by !abc)
 * - request.siteVersion = "efg", rule.siteVersion = ["efg", "!abc"] => rule is picked, "!abc" is ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["efg", "!abc"] => rule is not picked, "!abc" is ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["efg", "abc"] => rule is picked, exact match
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["abc"] => rule is not picked, insufficient match
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all"] => rule is picked, "all" accepts any value
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc"] => rule is not picked, "!abc" is excluded
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!empty"] => rule is picked, "!empty" is ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc", "!empty"] => rule is not picked, "!abc" is excluded
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc", "!empty", "abc"] => invalid never happens. Both "!abc" and "!abc" are not allowed in the same array.
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc", "!empty", "efg"] => rule is not picked, "!abc" is excluded
 * - request.siteVersion = "efg", rule.siteVersion = ["efg", "!abc"] => rule is picked (!abc ignored)
 * - request.siteVersion = "", rule.siteVersion = ["efg", "!empty"] => rule is NOT picked (excluded by !empty)
 * - request.siteVersion = "efg", rule.siteVersion = ["efg", "!empty"] => rule is picked (!empty ignored)
 * - request.siteVersion = "", rule.siteVersion = ["", "!empty"] => invalid never happens.
 * - request.siteVersion = "", rule.siteVersion = ["!empty"] => invalid never happens.
 * - request.siteVersion = "", rule.siteVersion = ["all", "!empty"] => rule is not picked (excluded by !empty)
 * - request.siteVersion = "<any valid value>", rule.siteVersion = ["all", "!empty"] => rule is picked (!empty ignored)
 *
 * @param fieldName - The name of the field to query.
 * @param valueToSearchFor - A comma-separated list of values to look for in the field of the document.
 * @returns A mongoose query object with $or conditions.
 */
export const queryFieldIncludesOrEmpty = (
  fieldName: KeysWithArrayValues<Rule>,
  valueToSearchFor: string
): mongoQueryType => {
  const conditions: QueryValues[] = [];

  if (valueToSearchFor) {
    // If request has a non-empty value, create a query to match rules that:
    // 1. Have ANY of the requested values in their array
    // 2. Do NOT have the negation of ANY requested values (e.g., if request has "k2", rule shouldn't have "!k2")
    // 3. Do NOT have "!empty" (since request is not empty)
    const splitValues = valueToSearchFor.split(',');
    const excluded = splitValues.map(v => `!${v}`);
    excluded.push(NO_EMPTY);

    conditions.push({ [fieldName]: { $elemMatch: { $in: splitValues, $nin: excluded } } });
  } else {
    // If request has an empty value, also match rules that have an array with empty string
    conditions.push({ [fieldName]: { $elemMatch: { $in: [''], $nin: [] } } });
  }

  // Always include rules with empty or undefined fields (matches "all" case - rules without restrictions)
  conditions.push({ [fieldName]: { $exists: false } }, { [fieldName]: { $eq: [] } }); // these line has to be adjusted to match the new logic. Always match rules with empty or undefined fields. Have to be changed to accommodate "all" | "!empty" cases.

  return { $or: conditions };
};

// these lines specify how I understand the logic, but they can be wrong.
// line 35 is not problematic, the values does not have to appear in the same order as in the rule.
// line 36 is not problematic, all values specified in the request have to be included in the rule. 
// line 19: rule specifies the request values that have to be included, for the rule to be picked. for example if rule has ["abc", "efg"] and request has "abc,efg", the rule is picked. If rule has ["abc", "efg"] and request has "abc,efg,hij", the rule is picked.
// line 37: exact value ["all"]. 
// Should we treat the literal string "all" in a rule array as a special keyword? Yes.

// If any of the requirements are not met, the function has to be adjusted to accommodate the new logic.


// This logic has to be met. If any of the other logic line are contradicting these lines, the contradictions has to be adjusted/overriden.{
// Rule A works when request.siteVersion doesn't contain "k2" 
// request.siteVersion: "ab_atsdk_ga,zoltan,k2" -> rule A will not be matched
// request.siteVersion: "k2" -> rule A will not be matched
// request.siteVersion: "" -> rule A will be matched
// request.siteVersion: "zoltan" -> rule A will be matched
//}
import { KeysWithArrayValues, Rule } from 'ads-layouts-tools';

const NO_EMPTY = '!empty' as const;

type QueryValues =
  | { [key: string]: { $exists: false } }
  | { [key: string]: { $eq: [] } }
  | { [key: string]: { $elemMatch: { $in: string[]; $nin: string[] } } };

type mongoQueryType = {
  $or?: QueryValues[];
};

/**
 * Generates a mongoose part of a query object for a field.
 *
 * Query allows rules that have the field:
 * - including any of the values
 * - empty or undefined
 * - as an empty array
 *
 * Additionally Exclude logic is applied:
 * - if request contains value that is excluded for specific key, rule is not picked from mongodb.
 * example: request.siteVersion = "abc", rule.siteVersion = ["!abc"] => rule is not picked
 * example: request.siteVersion = "efg", rule.siteVersion = ["efg", "!abc"] => rule is picked, "!abc" is ignored
 * example: request.siteVersion = "efg", rule.siteVersion = ["efg", "!abc"] => rule is picked, "!abc" is ignored
 * example: request.siteVersion = "efg,abc", rule.siteVersion = ["efg", "!abc"] => rule is not picked, "!abc" prevents match
 *
 * @param fieldName - The name of the field to query.
 * @param valueToSearchFor - A comma-separated list of values to look for in the field of the document. Assumed non empty value.
 * @returns A mongoose query object.
 */
export const queryFieldIncludesOrEmpty = (
  fieldName: KeysWithArrayValues<Rule>,
  valueToSearchFor: string
): mongoQueryType => {
  const splitValues = valueToSearchFor.split(',');
  const noEmpty = splitValues.includes(NO_EMPTY); // <= Taken from request - wrong approach
  const arr = splitValues.filter(v => v !== NO_EMPTY);

  const allowed = arr.filter(v => !v.startsWith('!'));
  const excluded = arr.filter(v => v.startsWith('!')); // <= Taken from request - wrong approach

  const conditions: QueryValues[] = [];

  if (allowed.length || excluded.length) {
    conditions.push({ [fieldName]: { $elemMatch: { $in: allowed, $nin: excluded } } });
  }

  if (!noEmpty) {
    conditions.push({ [fieldName]: { $exists: false } }, { [fieldName]: { $eq: [] } });
  }

  return conditions.length > 0 ? { $or: conditions } : {};
};

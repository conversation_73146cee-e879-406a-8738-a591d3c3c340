import { KeysWithArrayValues, Rule } from 'ads-layouts-tools';

const NO_EMPTY = '!empty' as const;

type QueryValues =
  | { [key: string]: { $exists: false } }
  | { [key: string]: { $eq: [] } }
  | { [key: string]: { $elemMatch: { $in: string[]; $nin: string[] } } };

type mongoQueryType = {
  $or?: QueryValues[];
};

/**
 * Generates a mongoose part of a query object for a field.
 *
 * Query allows rules that have the field: // these line has to be adjusted to match the new logic
 * - including any of the values // these line has to be adjusted to match the new logic
 * - empty or undefined // these line has to be adjusted to match the new logic
 * - as an empty array // these line has to be adjusted to match the new logic
 *
 * Additionally Exclude logic is applied:
 * - if request contains value that is excluded for specific key, rule is not picked from mongodb.
 * example: request.siteVersion = "abc", rule.siteVersion = ["!abc"] => rule is not picked
 * example: request.siteVersion = "efg", rule.siteVersion = ["efg", "!abc"] => rule is picked, "!abc" is ignored
 * example: request.siteVersion = "efg", rule.siteVersion = ["efg", "!abc"] => rule is picked, "!abc" is ignored
 * example: request.siteVersion = "", rule.siteVersion = ["efg", "!empty"] => rule is not picked, "!empty" prevents match
 * example: request.siteVersion = "efg", rule.siteVersion = ["efg", "!empty"] => rule is picked, "!empty" is ignored
 * example: request.siteVersion = "", rule.siteVersion = ["", "!empty"] => invalid never happens.
 * example: request.siteVersion = "", rule.siteVersion = ["!empty"] => invalid never happens.
 * example: request.siteVersion = "", rule.siteVersion = ["all", "!empty"] => rule is not picked, "!empty" prevents match
 * example: request.siteVersion = "<any valid value>", rule.siteVersion = ["all", "!empty"] => rule is picked, "!empty" is ignored (it only applies to empty values)
 *
 * @param fieldName - The name of the field to query.
 * @param valueToSearchFor - A comma-separated list of values to look for in the field of the document. Assumed non empty value.
 * @returns A mongoose query object.
 */
export const queryFieldIncludesOrEmpty = (
  fieldName: KeysWithArrayValues<Rule>,
  valueToSearchFor: string
): mongoQueryType => {
  const conditions: QueryValues[] = [];

  if (valueToSearchFor) {
    // If request has a non-empty value, create a query to match rules that:
    // 1. Have ANY of the requested values in their array
    // 2. Do NOT have the negation of ANY requested values (e.g., if request has "k2", rule shouldn't have "!k2")
    // 3. Do NOT have "!empty" (since request is not empty)
    const splitValues = valueToSearchFor.split(',');
    const excluded = splitValues.map(v => `!${v}`);
    excluded.push(NO_EMPTY);

    conditions.push({ [fieldName]: { $elemMatch: { $in: splitValues, $nin: excluded } } });
  } else {
    // If request has an empty value, also match rules that have an array with empty string
    conditions.push({ [fieldName]: { $elemMatch: { $in: [''], $nin: [] } } });
  }

  // Always include rules with empty or undefined fields (matches "all" case)
  conditions.push({ [fieldName]: { $exists: false } }, { [fieldName]: { $eq: [] } });// these line has to be adjusted to match the new logic

  return conditions.length > 0 ? { $or: conditions } : {};// these line has to be adjusted to match the new logic
};

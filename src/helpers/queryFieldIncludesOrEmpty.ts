

import { KeysWithArrayValues, Rule } from 'ads-layouts-tools';

const NO_EMPTY = '!empty' as const;

type QueryValues = object;
type mongoQueryType = {
  $or?: QueryValues[];
};

/**
 * Generates a mongoose part of a query object for a field.
 *
 * @param fieldName - The name of the field to query.
 * @param valueToSearchFor - A comma-separated list of values to look for in the field of the document.
 * @returns A mongoose query object.
 */
export const queryFieldIncludesOrEmpty = (
  fieldName: KeysWithArrayValues<Rule>,
  valueToSearchFor: string
) => {
  const arr = valueToSearchFor.split(',');
  return {
    $or: [
      { [fieldName]: { $elemMatch: { $in: arr } } },
      { [fieldName]: { $size: 0 } },
      { [fieldName]: { $exists: false } }
    ]
  };
};


/**
 * Generates a mongoose part of a query object for a field.
 *
 * Query logic (MongoDB $or condition):
 * 1. When request has a non-empty value:
 *    - Matches rules where the field array contains exactly the requested values and does not contain any negated values (e.g. "!abc") or "!empty"
 *    - BUT excludes rules where the array contains the negation (!value) or !empty
 * 2. When request has an empty value:
 *    - Matches rules where the (field array is empty and does not contain ["!empty"]) or (field array has "all" and is not excluded by any negated value (e.g. "!abc") or "!empty")
 * 3. Always matches rules where:
 *    - if rule has "all" and is not excluded by any negated value (e.g. "!abc") or "!empty"
 *
 * Exclusion logic (stored in rule arrays, not in requests):
 * - Rules can use !<value> to exclude specific values (e.g., ['zoltan', '!k2'] excludes k2)
 * - Rules can use !empty to require non-empty values (e.g., ['k2', '!empty'] requires k2 and non-empty)
 * - Rules with [] field are treated as "empty" arrays. (not equivalent to 'all')
 * - Query for Rules with undefined field are not considered, because this function does not deal with undefined fields. Invocation of this function with undefined field will never happen.
 *
 * Examples:
 * - request.siteVersion = "abc", rule.siteVersion = ["!abc"] => rule is NOT picked (excluded by !abc)
 * - request.siteVersion = "efg", rule.siteVersion = ["efg", "!abc"] => rule is picked, "!abc" is ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["efg", "!abc"] => rule is not picked, "!abc" is ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["efg", "abc"] => rule is picked, exact match
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["abc"] => rule is not picked, insufficient match
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all"] => rule is picked, "all" accepts any value
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc"] => rule is not picked, "!abc" is excluded
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!empty"] => rule is picked, "!empty" is ignored
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc", "!empty"] => rule is not picked, "!abc" is excluded
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc", "!empty", "abc"] => invalid never happens. Both "!abc" and "!abc" are not allowed in the same array.
 * - request.siteVersion = "abc,efg", rule.siteVersion = ["all", "!abc", "!empty", "efg"] => rule is not picked, "!abc" is excluded
 * - request.siteVersion = "efg", rule.siteVersion = ["efg", "!abc"] => rule is picked (!abc ignored)
 * - request.siteVersion = "", rule.siteVersion = ["efg", "!empty"] => rule is NOT picked (excluded by !empty)
 * - request.siteVersion = "efg", rule.siteVersion = ["efg", "!empty"] => rule is picked (!empty ignored)
 * - request.siteVersion = "", rule.siteVersion = ["", "!empty"] => invalid never happens.
 * - request.siteVersion = "", rule.siteVersion = ["!empty"] => invalid never happens.
 * - request.siteVersion = "", rule.siteVersion = ["all", "!empty"] => rule is not picked (excluded by !empty)
 * - request.siteVersion = "<any valid value>", rule.siteVersion = ["all", "!empty"] => rule is picked (!empty ignored)
 *
 * @param fieldName - The name of the field to query.
 * @param valueToSearchFor - A comma-separated list of values to look for in the field of the document.
 * @returns A mongoose query object with $or conditions.
 */
export const queryFieldIncludesOrEmptyForSiteVersion = (
  fieldName: KeysWithArrayValues<Rule>,
  valueToSearchFor: string
): mongoQueryType => {
  const conditions: QueryValues[] = [];
  const splitValues = valueToSearchFor ? valueToSearchFor.split(',') : [];

  if (valueToSearchFor) {
    // Non-empty request: Match rules where ANY request value is in whitelist
    // AND no exclusions are violated
    // Whitelist Logic (ANY matching per line 86):
    // - Rule array acts as whitelist of allowed values
    // - Request matches if it contains ANY value from rule's whitelist OR rule has "all"
    // - Exclusions (!value) reject if that specific value is in request
    // - !empty rejects empty requests (not applicable here since request is non-empty)

    const excluded = splitValues.map(v => `!${v}`);
    excluded.push(NO_EMPTY);

    // Match rules where:
    // - Rule contains at least one request value OR has "all"
    // - Rule doesn't contain exclusions for any request values
    // - Rule doesn't have "!empty" (would be violated by empty, but we're non-empty)
    conditions.push({
      [fieldName]: {
        $elemMatch: {
          $in: [...splitValues, 'all'],
          $nin: excluded
        }
      }
    });
  } else {
    // Empty request: Match rules that don't explicitly reject empty via "!empty"
    // Examples that should match:
    // - [] (empty array)
    // - ['zoltan', '!k2'] (has values but no !empty)
    // - ['all'] (wildcard)
    // Examples that should NOT match:
    // - ['k2', '!empty'] (has !empty)
    // - ['all', '!empty'] (has !empty)

    // Match rules with empty array
    conditions.push({ [fieldName]: { $eq: [] } });

    // Match rules that DON'T have '!empty' in their array
    // Using $not with $elemMatch to say "not contains '!empty'"
    conditions.push({
      [fieldName]: {
        $not: {
          $elemMatch: {
            $eq: NO_EMPTY
          }
        }
      }
    } as QueryValues);
  }

  // undefined fields are treated as empty arrays / [].
  conditions.push({ [fieldName]: { $exists: false } });

  return { $or: conditions };
};

// these lines specify how I understand the logic, but they can be wrong.
// line 35 is not problematic, the values does not have to appear in the same order as in the rule.
// line 36 is not problematic, all values specified in the request have to be included in the rule.
// line 19: rule specifies the request values that have to be included, for the rule to be picked. for example if rule has ["abc", "efg"] and request has "abc,efg", the rule is picked. If rule has ["abc", "efg"] and request has "abc,efg,hij", the rule is picked.
// line 37: exact value ["all"].

// Should we treat the literal string "all" in a rule array as a special keyword? Yes.
// Should [] match everything always? No.
// Should it NOT match (rule doesn't contain all requested values)? If request.siteVersion is empty, [] should match.

// If any of the requirements are not met, the function has to be adjusted to accommodate the new logic.

// This logic has to be met. If any of the other logic line are contradicting these lines, the contradictions has to be adjusted/overriden.{ START
// Rule A works when request.siteVersion doesn't contain "k2"
// request.siteVersion: "ab_atsdk_ga,zoltan,k2" -> rule A will not be matched <= sacred requirement
// request.siteVersion: "k2" -> rule A will not be matched <= sacred requirement
// request.siteVersion: "" -> rule A will be matched <= sacred requirement
// request.siteVersion: "zoltan" -> rule A will be matched <= sacred requirement
//} END

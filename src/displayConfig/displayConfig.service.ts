import { HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  CachePartsEnum,
  CustomHttpStatus,
  DisplayConfig,
  DisplayConfigDocument,
  DisplayConfigFiltersService,
  LogLevel
} from 'ads-layouts-tools';
import { deHydrateDocuments } from 'Helpers';
import { DisplayConfigKey } from 'InterfacesAndTypes';
import { Model } from 'mongoose';
import { CreateException, log } from 'Utils';
import { CacheService } from '../cacheModule/cache.service';
import { ENV } from '../envalidConfig';

@Injectable()
export class DisplayConfigService {
  constructor(
    @InjectModel(DisplayConfig.name)
    private DisplayConfigModel: Model<DisplayConfigDocument>,
    private readonly cache: CacheService,
    private readonly filtersService: DisplayConfigFiltersService
  ) {}

  async getReleaseVersion(
    serviceId: string,
    time: string,
    serviceEnv?: string,
    siteVersion?: string,
    omitCache = false
  ): Promise<string> {
    const displayConfigs = await this.selectDisplayConfigs(time, serviceId, omitCache);

    const releaseVersion = await this.filtersService.selectReleaseVersion({
      serviceId,
      serviceEnv,
      siteVersion,
      time,
      displayConfigs
    });

    const releaseLog = {
      releaseVersion,
      serviceId,
      serviceEnv,
      siteVersion
    };

    log('SELECTED_RELEASE', releaseLog, LogLevel.dev);

    if (!releaseVersion) {
      const errMsg = `WARN_${CustomHttpStatus.CANNOT_FIND_ANY_RELEASE}_CANNOT_FIND_ANY_RELEASE`;
      log(errMsg, releaseLog, LogLevel.warn);

      throw CreateException({
        message: `Cannot match any release to given serviceId: ${serviceId}, serviceEnv: ${serviceEnv}, siteVersion: ${siteVersion}. This problem in most cases is related to non-existing siteVersion. Make sure siteVersion "${siteVersion}" exist in config ${ENV.DISPLAY_CONFIG_URL}`,
        statusCode: CustomHttpStatus.CANNOT_FIND_ANY_RELEASE
      });
    }

    return releaseVersion;
  }

  async getUniqServiceIds(): Promise<string[]> {
    const uniqServiceIds: string[] = await this.DisplayConfigModel.distinct('service');
    if (!uniqServiceIds || uniqServiceIds.length == 0) {
      throw CreateException({
        message: 'DisplayConfigs data not found!',
        statusCode: HttpStatus.NOT_FOUND
      });
    }
    return uniqServiceIds;
  }

  async selectDisplayConfigs(
    time: string,
    serviceId: string,
    omitCache = false
  ): Promise<DisplayConfig[]> {
    const appCacheKey: DisplayConfigKey = `${CachePartsEnum.RELEASE}__${serviceId}__${time}`;

    let displayConfigDocuments = await this.cache.get(appCacheKey, omitCache);

    if (!displayConfigDocuments) {
      log('SELECT_RELEASE_VERSION_FETCH_FROM_DB', { appCacheKey });
      displayConfigDocuments = await this.DisplayConfigModel.find({
        service: serviceId
      });

      if (displayConfigDocuments.length > 0) {
        await this.cache.set(appCacheKey, displayConfigDocuments, omitCache);
      }
    }

    return deHydrateDocuments(displayConfigDocuments);
  }
}

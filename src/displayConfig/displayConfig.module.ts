import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  DisplayConfig,
  DisplayConfigFiltersModule,
  DisplayConfigSchema
} from 'ads-layouts-tools';
import { log } from 'Utils';
import { DisplayConfigService } from './displayConfig.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: DisplayConfig.name,
        schema: DisplayConfigSchema
      }
    ]),
    DisplayConfigFiltersModule.configure(log)
  ],
  providers: [DisplayConfigService],
  exports: [DisplayConfigService]
})
export class DisplayConfigModule {}

import { ApiProperty } from '@nestjs/swagger';
import {
  ServiceEnvEnum,
  AdConfigDeviceTypeEnum,
  AdConfigAdServerEnum,
  AccessModelEnum,
  PaywallEnum
} from 'ads-layouts-tools';
import { BodyElement, CommonRequest, ContentMeta, PageTypeEnum } from 'InterfacesAndTypes';

// Response
class AdConfigActivationThresholds {
  @ApiProperty({ default: '200px' })
  offset!: string;

  @ApiProperty({ default: 0 })
  percent!: number;

  @ApiProperty({ default: 0 })
  delay!: number;
}

export class GeneratorResponseModel {
  @ApiProperty({ default: 'slot-article-2' })
  id!: string;

  @ApiProperty({
    default: 'placeholder'
  })
  type!: string;

  @ApiProperty({ default: '300px' })
  width!: string;

  @ApiProperty({ default: '600px' })
  height!: string;

  @ApiProperty({
    enum: AdConfigAdServerEnum,
    default: AdConfigAdServerEnum.GAM
  })
  adServer!: AdConfigAdServerEnum;

  @ApiProperty({ default: ['bider1', 'bider2'] })
  bidders!: string[];

  @ApiProperty({
    type: AdConfigActivationThresholds,
    default: {
      offset: '200px',
      percent: 0,
      delay: 0
    }
  })
  activationThresholds!: AdConfigActivationThresholds;
}

export class ContentMetaModel implements ContentMeta {
  @ApiProperty({
    required: true,
    enum: AdConfigDeviceTypeEnum,
    type: String
  })
  deviceType!: AdConfigDeviceTypeEnum;

  @ApiProperty({
    required: true,
    type: String
  })
  locationInfoPageType!: string;

  @ApiProperty({
    required: true,
    type: String
  })
  locationInfoSectionId!: string;

  @ApiProperty({
    required: true,
    type: String
  })
  locationInfoSectionName!: string;

  @ApiProperty({
    required: false,
    anyOf: [
      { enum: Object.values(ServiceEnvEnum) },
      { pattern: `^${ServiceEnvEnum.STAGE}\\d+$` },
      { pattern: 'prev-.*' }
    ],
    type: String
  })
  serviceEnv?: string;

  @ApiProperty({
    required: true,
    type: String
  })
  serviceId!: string;

  @ApiProperty({
    default: '',
    type: String
  })
  siteVersion?: string;

  @ApiProperty({
    required: true,
    type: String
  })
  time!: string;

  @ApiProperty({
    required: true,
    type: String
  })
  siteVersionIdentifier!: string;

  @ApiProperty({
    required: true,
    type: String
  })
  locationInfoPageId!: string | null;

  @ApiProperty({
    type: String
  })
  rulesPackage?: string;

  @ApiProperty({
    required: false,
    enum: AccessModelEnum,
    type: String
  })
  accessModel?: AccessModelEnum;

  @ApiProperty({
    required: false,
    enum: PaywallEnum,
    type: String
  })
  paywall?: PaywallEnum;

  @ApiProperty({
    type: String
  })
  serviceName?: string;
}

export class CommonRequestModel implements CommonRequest {
  @ApiProperty({
    required: true,
    enum: PageTypeEnum,
    default: PageTypeEnum.article
  })
  type!: PageTypeEnum;

  @ApiProperty({
    required: true,
    type: ContentMetaModel
  })
  meta!: ContentMetaModel;

  @ApiProperty({ required: true })
  elements!: BodyElement[];

  @ApiProperty({ required: true })
  direction!: string;
}

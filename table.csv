<PERSON><PERSON><PERSON>,request: brak siteVersion,request: siteVersion: "zoltan",request: siteVersion: "zoltan;k2",request: siteVersion: "k2",request: siteVersion: "inna;zoltan"
"reguła z warunkiem: gdy siteVersion nie zawiera ""k2"" siteVersion: [ 'zoltan' '!k2' ]",V,V,X,X,V
"reguła z warunkiem: gdy siteVersion zawiera ""k2"" siteVersion: [ 'abc', 'k2', '!empty' ]",X,X,V,V,X
"reguła z warunkiem: gdy siteVersion jest równe ""k2"" siteVersion: [ 'k2', '!empty' ]",X,X,X,V,X
"reguła z warunkiem: gdy siteVersion nie jest równe ""k2"" siteVersion: <tablica z adekwatnym siteVersion> ([ 'zoltan', '!k2' ])",V,V,V,X,V
"reguła z warunkiem: gdy siteVersion istnieje i ma dowolną wartość siteVersion: <tablica z adekwatnym siteVersion> ([ 'zoltan', '!empty' ])",X,V,V,V,V
"w regule nie ma warunku opartego o siteVersion siteVersion: [ 'all' ]",V,V,V,V,V
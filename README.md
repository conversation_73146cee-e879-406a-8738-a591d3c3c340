## Documentation

- [Ads Layouts dokumentacja użytkownika](https://confluence.pl.grupa.iti/pages/viewpage.action?pageId=175618546)

## Installation

```bash
$ npm ci
```

## Running the app

```bash
# development
$ npm run start

# development watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Hooks Setup

File **.husky/\_/hooks.cfg** contains the variables used in git hooks logic. Set up automatically when script 'h' is executed.

**ENABLE_LOGS**: sets whether the logs from the hooks should be displayed. Errors and warnings are always displayed.

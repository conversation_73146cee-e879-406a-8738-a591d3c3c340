#!/bin/sh

# Post-Push Hook - Runs npm audit after successful push
# This script is triggered by the reference-transaction hook after a successful push

#load hooks.cfg file variables if present. If not, make a config file. 
if [ ! -f ".husky/_/hooks.cfg" ]; then
    echo "hooks.cfg file not found. Skipping post-push hook."
    return
fi

# Read config file
# shellcheck disable=SC1091
. .husky/_/hooks.cfg


# Colors for output
LOG_ERR='\033[0;31m' # RED
LOG_OK='\033[0;32m' # GREEN
LOG_WARN='\033[1;33m' # YELLOW
LOG_INFO='\033[0;34m' # BLUE
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    # Check if logging is enabled and is not warn or error
    if [ "$ENABLE_LOGS" = "false" ] && [ "$1" != "${LOG_WARN}" ] && [ "$1" != "${LOG_ERR}" ]; then
        return
    fi
    printf "${1}[POST-PUSH HOOK]${NC} %s\n" "$2"
}

print_colored "${LOG_INFO}" "Post-push hook triggered - running npm audit..."

AUDIT_COMMAND="npm audit --json 2>/dev/null"
FIXABLE_AUDIT_REPORT_JSON=$(eval "$AUDIT_COMMAND")
AUDIT_EXIT_CODE=$?

# Check if audit command itself failed
if [ $AUDIT_EXIT_CODE -ne 0 ] && [ -z "$FIXABLE_AUDIT_REPORT_JSON" ]; then
    print_colored "${LOG_ERR}" "Command failed: $AUDIT_COMMAND"
    exit 1
fi

# Check if jq is available for JSON parsing
if command -v jq >/dev/null 2>&1; then
    # Use jq to check if there are any fixable actions in the dry-run report
    
    # 1. Gets vulnerabilities object (or empty if missing)
    # 2. Extracts fixAvailable values
    # 3. Filters only the true values
    # 4. Counts the number of occurrences.
    FIXABLE_COUNT=$(echo "$FIXABLE_AUDIT_REPORT_JSON" | jq '([.vulnerabilities // {} | to_entries[] | .value.fixAvailable] | map(select(. == true)) | length)')
else
    print_colored "${LOG_INFO}" "jq not found. Falling back to less reliable 'grep' check."
    print_colored "${LOG_INFO}" "install jq with 'brew install jq'"
    FIXABLE_COUNT=$(echo "$FIXABLE_AUDIT_REPORT_JSON" | grep -c 'fixAvailable.*true')
fi

# Display results based on fixable vulnerabilities found
if [ "$FIXABLE_COUNT" -gt 0 ]; then
    print_colored "${LOG_WARN}" "'npm audit' found fixable vulnerabilities in project dependencies."
    print_colored "${LOG_WARN}" "Please run 'npm audit fix' locally to resolve these issues."
else
    print_colored "${LOG_OK}" "npm audit found no fixable vulnerabilities."
fi

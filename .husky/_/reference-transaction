#!/bin/sh

#load hooks.cfg file variables if present. If not, make a config file. 
if [ ! -f ".husky/_/hooks.cfg" ]; then
    echo "Creating hooks.cfg file..."
    echo "ENABLE_LOGS=true" > .husky/_/hooks.cfg
fi

# Reference Transaction Hook - Detects when remote tracking branches are updated after push

POST_PUSH_SCRIPT=".husky/_/post-push"

# Read the reference transaction data
while read -r _ _ refname
do
    # Check if this is a committed transaction on a remote tracking branch
    if [ "$1" = "committed" ] && echo "$refname" | grep -q "^refs/remotes/.*/.*"
    then
        # Extract remote and branch from refname (e.g., refs/remotes/origin/main)
        remote_and_branch=${refname#refs/remotes/}
        remote=${remote_and_branch%%/*}
        
        # Only trigger for pushes to origin
        if [ "$remote" = "origin" ]
        then
            # Execute the post-push script if it exists
            if [ -f "$POST_PUSH_SCRIPT" ]
            then
                exec "$POST_PUSH_SCRIPT"
            fi
        fi
    fi
done
